// Shows a summary of certain requirements (natural light, ventilation, air movement etc)
// for all zones within the building, broken down in several ways.
(function () {

    'use strict';
    angular
        .module('app')
        .component('zoneSummary', {
            bindings: {
                source: '<',                // Actual source object which CONTAINS zones
                storeys: '<',               // Array of Storeys within the Building/Source
                nccClimateZone: '<',
                nathersClimateZone: '<',
                optionIndex: '<',           // Index of the option (chart div ids need to be unique)
                certification: '<',
                sectorDetermination: '<',
                envelopeSummarySave: '=',
                zoneSummarySave: '=',
            },
            templateUrl: 'app/ui/assessment/zone-summary.html',
            controller: ZoneSummary,
            controllerAs: 'vm'
        });

    ZoneSummary.$inject = ['$scope', '$rootScope', 'common', 'compliancemethodservice', 'zonetypeservice', 'zoneservice', 'zonesummaryservice', 'coreLoop', 'uuid4'];

    function ZoneSummary($scope, $rootScope, common, compliancemethodservice, zonetypeservice, zoneservice, zonesummaryservice, coreLoop, uuid4) {

        var vm = this;
        vm.zoneTypeList = [];
        vm.nccClassificationList = [];
        vm.zoneActivityList = [];

        vm.buildingSummaryGroups = {};
        vm.lightAndVentilationStoreyRows = [];

        vm.knownBuildingSummaryGroups = zonesummaryservice.knownBuildingSummaryGroups;

        vm.sectorFromLabel = zonesummaryservice.sectorFromLabel;
        vm.isSectorNotEmpty = zonesummaryservice.isSectorNotEmpty;

        vm.selectedStoreys = null;

        // As the same chart is in multiple tabs they require unique ids.
        vm.windRoseChartId = 'es-windrose-chart-' + vm.optionIndex;

        vm.sectionExpansions = {}; // Used to keep track of which sections are expanded.

        // Zone Summary tab selection - always start at first tab (index 0)
        vm.zoneSummarySelectedTabIndex = 0;

        // Envelope Summary
        vm.GROUP_OPTIONS = zonesummaryservice.groupOptions;
        // Always create fresh copies of default filters to ensure clean state on component recreation
        vm.filters = angular.copy(zonesummaryservice.defaultFilters);
        vm.applySelectionLogic = function (filter, shading) {
            zonesummaryservice.applySelectionLogic(filter, shading, () => vm.calculateEnvelopeSummaryData());
            // Force refresh (Was taking multiple seconds to reflect change otherwise...?)
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }
        vm.applyStoreySelectLogic = zonesummaryservice.applyStoreySelectLogic;
        vm.applyGroupSelectLogic = zonesummaryservice.applyGroupSelectLogic;
        vm.windRoseChartView = 'ExteriorGlazing';

        vm.envelopeSummaryData = {
            sectorKeys: []
        };

        let firstRun = true;
        /** Retrieve and initialize any required data here, not just floating around the controller. */
        function initialize() {

            zonetypeservice.getList().then(data => vm.zoneTypeList = data.data);
            zoneservice.getNccClassificationList().then(data => vm.nccClassificationList = data);
            zoneservice.getZoneActivityList().then(data => vm.zoneActivityList = data);

            // Select "All" in each Shading field by default
            zonesummaryservice.applySelectionLogic(vm.filters[0].wallHorizontalShading,    vm.filters[0].wallHorizontalShading.selectableArray[0]);
            zonesummaryservice.applySelectionLogic(vm.filters[0].glazingHorizontalShading, vm.filters[0].glazingHorizontalShading.selectableArray[0]);
            zonesummaryservice.applySelectionLogic(vm.filters[0].wallVerticalShading,      vm.filters[0].wallVerticalShading.selectableArray[0]);
            zonesummaryservice.applySelectionLogic(vm.filters[0].glazingVerticalShading,   vm.filters[0].glazingVerticalShading.selectableArray[0]);

            vm.calculationTimerPromise = setInterval(() => {
                coreLoop.computeZoneRequirementsAndAchievedRatings(vm.source, vm.nccClimateZone);
                coreLoop.computeSpaceRequirementsAndAchievedRatings(vm.source);
                vm.buildingSummaryGroups = zonesummaryservice.constructBuildingSummaryGroups(
                    vm.source,
                    vm.storeys,
                    vm.zoneActivityList,
                    vm.zoneTypeList,
                    vm.nccClassificationList
                );
                // Saving particular data according to THR-667
                let habitableWB = vm.buildingSummaryGroups.habitable.rows[vm.buildingSummaryGroups.habitable.rows.length-1].zones[0];
                let conditionedWB = vm.buildingSummaryGroups.conditioned.rows[vm.buildingSummaryGroups.habitable.rows.length-1].zones[0];
                let houseClass1aWB = vm.buildingSummaryGroups.nccClassification.rows[vm.buildingSummaryGroups.habitable.rows.length-1].zones.find(z => z.description == "Class 1a");
                vm.zoneSummarySave = {
                    habitable: {
                        zoneDescription: "Habitable",
                        floorArea: habitableWB.floorArea,
                        volume: habitableWB.volume,
                        exteriorWallArea: habitableWB.exteriorWallArea,
                        exteriorGlazingArea: habitableWB.exteriorGlazingArea,
                        lampPowerMaximumW: habitableWB.lampPowerMaximumW,
                        glassExteriorWallAreaPercent: habitableWB.glassExteriorWallAreaPercent,
                        glassFloorAreaPercent: habitableWB.glassFloorAreaPercent
                    },
                    conditioned: {
                        zoneDescription: "Conditioned",
                        floorArea: conditionedWB.floorArea,
                        volume: conditionedWB.volume,
                        exteriorWallArea: conditionedWB.exteriorWallArea,
                        exteriorGlazingArea: conditionedWB.exteriorGlazingArea,
                        lampPowerMaximumW: conditionedWB.lampPowerMaximumW,
                        glassExteriorWallAreaPercent: conditionedWB.glassExteriorWallAreaPercent,
                        glassFloorAreaPercent: conditionedWB.glassFloorAreaPercent
                    },
                    houseClass1a: {
                        zoneDescription: "House (Class 1a)",
                        floorArea: houseClass1aWB.floorArea,
                        volume: houseClass1aWB.volume,
                        exteriorWallArea: houseClass1aWB.exteriorWallArea,
                        exteriorGlazingArea: houseClass1aWB.exteriorGlazingArea,
                        lampPowerMaximumW: houseClass1aWB.lampPowerMaximumW,
                        glassExteriorWallAreaPercent: houseClass1aWB.glassExteriorWallAreaPercent,
                        glassFloorAreaPercent: houseClass1aWB.glassFloorAreaPercent
                    }
                };
                constructLightAndVentilationStoreyRows(vm.storeys);
                coreLoop.computeChenathAreaCorrectionFactor(vm.source, vm.nathersClimateZone);
                constructNccWholeOfHomeAreaCorrectionFactor(vm.source);

                let habitableZones = vm.source?.zones?.filter(z => z.zoneType?.zoneTypeCode === "ZTHabitableRoom");
                let floorArea = habitableZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
                vm.source.heatingLoadLimitCorrectionFactor = compliancemethodservice.constructHeatingLoadLimitAreaCorrectionFactor(floorArea);
                vm.source.coolingLoadLimitCorrectionFactor = compliancemethodservice.constructCoolingLoadLimitAreaCorrectionFactor(floorArea);

                // Only do once. Needs to be done after the above calculations.
                if (firstRun) {
                    // Only take storeys that have glazing when determining this value.
                    const storeysWithGlazing = zoneservice.determineStoreysWithGlazing(vm.source.openings);
                    vm.selectedStoreys = vm.source.storeys?.filter(x => storeysWithGlazing.some(y => y === x.floor));
                    // Run dynamic dropdown rules for their initial values. (also refreshes Envelope Summary Data)
                    vm.applyStoreySelectLogic(vm.source, vm.filters[0], () => vm.calculateEnvelopeSummaryData());
                    firstRun = false;
                }
            },
            1000);

        }

        // Need to recalculate data if vm.sectorDetermination changes
        $scope.$watch('vm.sectorDetermination', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                vm.calculateEnvelopeSummaryData(); 
            } 
         });

        $scope.$watch('vm.source.scratchFileProcessedWatchProperty', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                vm.calculateEnvelopeSummaryData();
                const storeysWithGlazing = zoneservice.determineStoreysWithGlazing(vm.source.openings);
                vm.selectedStoreys = vm.source.storeys?.filter(x => storeysWithGlazing.some(y => y === x.floor));
                vm.applyStoreySelectLogic(vm.source, vm.filters[0], () => vm.calculateEnvelopeSummaryData());
            }
        });

        vm.calculateEnvelopeSummaryData = function () {
            // Timeout stops angular being 1 update behind...
            setTimeout(() => {
                vm.envelopeSummaryData = zonesummaryservice.calculateEnvelopeSummaryData(
                    vm.source,
                    vm.sectorDetermination,
                    vm.filters
                );
                generateWindRoseData();
                if (vm.envelopeSummarySave == null) {
                    vm.envelopeSummarySave = {
                        sectorKeys: vm.envelopeSummaryData.sectorKeys,
                        exteriorWallAreaTotalsPerSector: vm.envelopeSummaryData.exteriorWallAreaTotalsPerSector,
                        exteriorGlazingAreaTotalsPerSector: vm.envelopeSummaryData.exteriorGlazingAreaTotalsPerSector,
                        glassExteriorWallRatioPerSector: vm.envelopeSummaryData.glassExteriorWallRatioPerSector,
                        averageGlazingUValuePerSector: vm.envelopeSummaryData.averageGlazingUValuePerSector,
                        averageGlazingSHGCPerSector: vm.envelopeSummaryData.averageGlazingSHGCPerSector,
                        windRoseChartSvg: saveChart(vm.windRoseChart)
                    };
                }
            },
            30);
        }

        function saveChart(chart) {
            // Get the chart's SVG code
            var svg = chart.getSVG({
                exporting: {
                    // Have to set to original value otherwise looks to be changing depending on screen size
                    sourceWidth: 878,
                    sourceHeight: 600
                    // sourceWidth: chart.chartWidth,
                    // sourceHeight: chart.chartHeight
                }
            });
            return svg;
        }

        $scope.$on("$destroy", function () {
            if (vm.calculationTimerPromise) {
                clearInterval(vm.calculationTimerPromise);
            }
        });

        vm.windRoseChartViewChange = function windRoseChartViewChange() {
            setTimeout(() => {
                generateWindRoseData();
            },
            30);
        }

        /**
         * Generates the data needed for the Wind Rose Data for the Envelope Summary section.
         * Generated from the Exterior Glazing Area Totals per Sector
         * Format: [ [windDirection1, windSpeed1], [windDirection2, windSpeed2], ... ]
         * 
        */
        function generateWindRoseData() {

            let dataToUse = [];
            let isGlassExteriorWallRatio = false

            // Data we inject now depends on the value of the vm.view dropdown (vm.windRoseChartView)
            switch (vm.windRoseChartView) {
                case 'ExteriorWall':
                    dataToUse = vm.envelopeSummaryData.exteriorWallAreaTotalsPerSector;
                    break;
                case 'ExteriorGlazing':
                    dataToUse = vm.envelopeSummaryData.exteriorGlazingAreaTotalsPerSector;
                    break;
                case 'GlassExteriorWallRatio':
                    dataToUse = vm.envelopeSummaryData.glassExteriorWallRatioPerSector;
                    isGlassExteriorWallRatio = true;
                    break;
                default:
                    throw ('Error - Unknown windRoseChartView: ' + vm.windRoseChartView);
                    break;
            }

            let percentageData = [];
            // Need both as GlassExteriorWallRatio tooltip displays both.
            // key/val pairs
            let wallAreaDataLookup = {};
            let glazingAreaDataLookup = {};
            for (const sector in dataToUse) {

                if (sector === 'total')
                    continue; // Skip

                if (dataToUse.hasOwnProperty(sector)) {
                    if (isGlassExteriorWallRatio) { // Doesn't have child fields
                        percentageData.push( [getSectorAngle(sector), parseFloat(dataToUse[sector].toFixed(2))] );
                    }
                    else {
                        percentageData.push( [getSectorAngle(sector), parseFloat(dataToUse[sector].percentage.toFixed(2))] );
                    }
                    // s = ne but exteriorWallAreaTotalsPerSector doesnt have key...
                    wallAreaDataLookup[getSectorAngle(sector)] = parseFloat(vm.envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].area.toFixed(2));
                    glazingAreaDataLookup[getSectorAngle(sector)] = parseFloat(vm.envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].area.toFixed(2));
                }
            }

            vm.windRosePercentageData = percentageData;
            vm.wallAreaDataLookup = wallAreaDataLookup;
            vm.glazingAreaDataLookup = glazingAreaDataLookup;

            generateWindRoseChart();
        }

        /**
         * Generate the Sector numberic value from its string label
         * 
         * @param   {string} sector ie. 'n' | 'e' | 's' | 'w'
         * @returns {number} Sector's numeric value
        */
        function getSectorAngle(sector) {

            let angle = null;
            const found = vm.sectorDetermination?.sectors.find(s => sector === s.label.toLowerCase());
            if (found) {
                if (found.min === 0 || found.max === 360) // N
                    angle = 0;
                else
                    angle = (found.max - found.min) / 2 + found.min; // Middle of min and max
            }
            else
                throw ('Error - Cannot match sector - sector:' + sector + ' sectorDeterminations:' + vm.sectorDetermination.sectors);

            return angle;
        }

        function constructLightAndVentilationStoreyRows(storeys) {

            let lightAndVentilationStoreyRows = [];

            storeys?.forEach(storey => {

                // Step 1.4 Add a single SUM row for Zonetype = Habitable
                let habitablRow = createSumRowMatching(
                    storey.name,
                    'zoneType.zoneTypeCode',
                    'ZTHabitableRoom',
                    storey.floor
                );

                if (habitablRow != null)
                    lightAndVentilationStoreyRows.push(habitablRow);

            });

            let sameOrSmaller = true; // By default we assume the requirements array is the same size or smaller (i.e. OK to angular.merge)

            if (vm.lightAndVentilationStoreyRows == null || (vm.lightAndVentilationStoreyRows.length < lightAndVentilationStoreyRows.length))
                sameOrSmaller = false; // Ok, need to blow away old value...

            if (sameOrSmaller)
                angular.merge(vm.lightAndVentilationStoreyRows, lightAndVentilationStoreyRows);
            else {
                vm.lightAndVentilationStoreyRows = lightAndVentilationStoreyRows; // Blow entire thing away - will cause UI problem (only noticeable if an element is open)
            }

        }

        /**
         *  Creates a row with data being the SUMMED value of all zones that match the
         *  given path/value combination.
         * @param {any} description A description to name the row (assuming any matches)
         * @param {any} pathToMatch A string to the path of the value we wish to match against.
         * @param {any} valueToMatch The value we want our path to equal to be included.
         * @param {any} floor The INT floor we wish to match with.
         * @param {any} excludeExterior If TRUE, excludes an zone where Zone Activity = TRUE from being included in totals and calculations.
         */
        function createSumRowMatching(description, pathToMatch, valueToMatch, floor, excludeExterior, totalFloorArea) {

            let row = {};

            // Get all zones matching our filter.
            let matchingZones = [];

            // If a single string has been passed in simply match against it
            if (!Array.isArray(valueToMatch)) {
                matchingZones = filterZones(
                    pathToMatch,
                    valueToMatch,
                    floor);
            } else {

                // If an array of possible values has been passed in, 
                // match against all of them and add together.
                for (let i = 0; i < valueToMatch.length; i++) {
                    let temp = filterZones(
                        pathToMatch,
                        valueToMatch[i],
                        floor);

                    matchingZones = matchingZones.concat(temp);
                }
            }

            if (matchingZones == null || matchingZones.length == 0)
                return null;

            row.zoneNumber = ""; // Not an actual zone, so blank.
            row.description = description;
            row.zoneActivity = zonesummaryservice.determineZoneActivitySum(matchingZones, vm.zoneActivityList);
            row.zoneType = zonesummaryservice.determineZoneTypeSum(matchingZones, vm.zoneTypeList);
            row.conditioned = zonesummaryservice.determineConditionedSum(matchingZones);
            row.nccClassification = zonesummaryservice.determineNccClassificationSum(matchingZones, vm.nccClassificationList);
            row.floorArea = matchingZones.map(x => x.floorArea).reduce((a, b) => a + b);
            row.floorAreaPercent = ((row.floorArea / totalFloorArea) * 100);

            // For individual rows, this data is already pre-calculated and in
            // the zone object itself, so we can use as-is.
            row.naturalLight = {
                requiredPercent: zonesummaryservice.determineSameOrVaries(matchingZones, 'naturalLightRequiredPercent'),
                required: matchingZones.map(x => x.naturalLightRequiredM2).reduce((a, b) => a + b),
                achieved: matchingZones.map(x => x.naturalLightAchievedM2).reduce((a, b) => a + b),
                outcome: zonesummaryservice.determinePassOrFail(matchingZones, 'naturalLightRequiredM2', 'naturalLightAchievedM2')
            };

            row.ventilation = {
                requiredPercent: zonesummaryservice.determineSameOrVaries(matchingZones, 'ventilationRequiredPercent'),
                required: matchingZones.map(x => x.ventilationRequiredM2).reduce((a, b) => a + b),
                achieved: matchingZones.map(x => x.ventilationAchievedM2).reduce((a, b) => a + b),
                outcome: zonesummaryservice.determinePassOrFail(matchingZones, 'ventilationRequiredM2', 'ventilationAchievedM2')
            };

            row.airMovement = {
                requiredPercent: zonesummaryservice.determineSameOrVaries(matchingZones, 'airMovementRequiredPercent'),
                required: matchingZones.map(x => x.airMovementRequiredM2).reduce((a, b) => a + b),
                achieved: matchingZones.map(x => x.airMovementAchievedM2).reduce((a, b) => a + b),
                outcome: zoneservice.calculateAirMovementOutcomeForZones(matchingZones)
            };

            //row.lampPower = {
            //    maximumWM2: zonesummaryservice.determineSameOrVaries(matchingZones, 'lampPowerMaximumWM2'),
            //    maximumW: matchingZones.map(x => x.lampPowerMaximumW).reduce((a, b) => a + b),
            //    achieved: "-",
            //};

            return row;
        }

        /** Filters zones by the given path/value combination and OPTIONALLY the storey. */
        function filterZones(path, value, storey = null) {

            let filtered = null;

            if (storey != null) {
                filtered = vm.source.zones
                    ?.filter(x => common.resolve(path, x) == value &&
                        x.storey == storey);
            } else {
                filtered = vm.source.zones
                    ?.filter(x => common.resolve(path, x) == value);
            }

            return filtered;
        }

        vm.airMovementOutcome = (required, achieved) => zoneservice.airMovementOutcome(required, achieved);

        vm.expand = function (section) {
            if (vm.source == null || vm.source.zones.length == 0)
                return;

            vm.sectionExpansions[section] = !vm.sectionExpansions[section];

            // Do calculations once when section expanded
            if (section == 'envelopeSummary') {
                vm.calculateEnvelopeSummaryData();
            }
        }

        vm.interiorZones = () => zoneservice.interiorZones(vm.source?.zones);
        vm.artificialLightingZones = () => [...zoneservice.interiorZones(vm.source?.zones), ...spaceTypesNeededInArtificialLighting()]
        vm.habitableZones = () => vm.source?.zones?.filter(z => z.zoneType?.zoneTypeCode === "ZTHabitableRoom");

        function spaceTypesNeededInArtificialLighting() {
            return vm.source.spaces.filter(x => x.zoneType?.zoneTypeCode === 'ZTClass10A' || 
                                                x.zoneType?.zoneTypeCode === 'ZTOutdoor');
        }

        const nccWholeAreaStaticLookupTable = [
            { min:  50, max:  59.999999, factor: 0.0119 },
            { min:  60, max:  69.999999, factor: 0.0116 },
            { min:  70, max:  79.999999, factor: 0.0113 },
            { min:  80, max:  89.999999, factor: 0.0111 },
            { min:  90, max:  99.999999, factor: 0.0108 },
            { min: 100, max: 109.999999, factor: 0.0106 },
            { min: 110, max: 119.999999, factor: 0.0105 },
            { min: 120, max: 129.999999, factor: 0.0103 },
            { min: 130, max: 139.999999, factor: 0.0101 },
            { min: 140, max: 149.999999, factor: 0.0100 },
            { min: 150, max: 159.999999, factor: 0.0099 },
            { min: 160, max: 169.999999, factor: 0.0097 },
            { min: 170, max: 179.999999, factor: 0.0096 },
            { min: 180, max: 189.999999, factor: 0.0095 },
            { min: 190, max: 199.999999, factor: 0.0094 },
            { min: 200, max: 209.999999, factor: 0.0093 },
            { min: 210, max: 219.999999, factor: 0.0092 },
            { min: 220, max: 229.999999, factor: 0.0091 },
            { min: 230, max: 239.999999, factor: 0.0090 },
            { min: 240, max: 249.999999, factor: 0.0090 },
            { min: 250, max: 259.999999, factor: 0.0089 },
            { min: 260, max: 269.999999, factor: 0.0088 },
            { min: 270, max: 279.999999, factor: 0.0087 },
            { min: 280, max: 289.999999, factor: 0.0087 },
            { min: 290, max: 299.999999, factor: 0.0086 },
            { min: 300, max: 309.999999, factor: 0.0085 },
            { min: 310, max: 319.999999, factor: 0.0085 },
            { min: 320, max: 329.999999, factor: 0.0084 },
            { min: 330, max: 339.999999, factor: 0.0083 },
            { min: 340, max: 349.999999, factor: 0.0083 },
            { min: 350, max: 359.999999, factor: 0.0082 },
            { min: 360, max: 369.999999, factor: 0.0082 },
            { min: 370, max: 379.999999, factor: 0.0081 },
            { min: 380, max: 389.999999, factor: 0.0081 },
            { min: 390, max: 399.999999, factor: 0.0080 },
            { min: 400, max: 409.999999, factor: 0.0080 },
            { min: 410, max: 419.999999, factor: 0.0079 },
            { min: 420, max: 429.999999, factor: 0.0079 },
            { min: 430, max: 439.999999, factor: 0.0078 },
            { min: 440, max: 449.999999, factor: 0.0078 },
            { min: 450, max: 459.999999, factor: 0.0077 },
            { min: 460, max: 469.999999, factor: 0.0077 },
            { min: 470, max: 479.999999, factor: 0.0077 },
            { min: 480, max: 489.999999, factor: 0.0076 },
            { min: 490, max: 499.999999, factor: 0.0076 },
        ];

        // Refer to THR-155
        function constructNccWholeOfHomeAreaCorrectionFactor(building) {

            const area = building.conditionedFloorArea;

            if (area > 500)
                vm.nccWholeOfHomeCorrectionFactor = 0.0075;
            else if (area < 50)
                vm.nccWholeOfHomeCorrectionFactor = 0.0075;
            else {
                const match = nccWholeAreaStaticLookupTable
                    .filter(f => area >= f.min && area <= f.max)[0];
                vm.nccWholeOfHomeCorrectionFactor = match.factor;
            }

            return vm.nccWholeOfHomeCorrectionFactor;
        }

        // Lookup labels unfortunately have to be added manually.
        const sectorLabels = {
            n: 'North',
            nne: 'North-North-East',
            ne: 'North-East',
            ene: 'East-North-East',
            e: 'East',
            ese: 'East-South-East',
            se: 'South-East',
            sse: 'South-South-East',
            s: 'South',
            ssw: 'South-South-West',
            sw: 'South-West',
            wsw: 'West-South-West',
            w: 'West',
            wnw: 'West-North-West',
            nw: 'North-West',
            nnw: 'North-North-West'
        };

        function generateWindRoseChart() {

            // Degree Labels
            const categories = vm.envelopeSummaryData.sectorKeys.filter(key => key !== 'total').map(key => sectorLabels[key]);
            const tickSize = 360 / categories.length; // ie. 360 / 8 = 45 degrees

            // Series Name
            let seriesName = '';
            if (vm.windRoseChartView === 'ExteriorWall')
                seriesName = 'Exterior Wall (%)';
            else if (vm.windRoseChartView === 'ExteriorGlazing')
                seriesName = 'Exterior Glazing (%)';
            else if (vm.windRoseChartView === 'GlassExteriorWallRatio')
                seriesName = 'Glass-Exterior Wall Ratio (%)';

            vm.windRoseChart = Highcharts.chart(vm.windRoseChartId, {
                series: [{ 
                    name: seriesName,
                    data: vm.windRosePercentageData
                }],
                chart: {
                    polar: true,
                    type: 'column',
                    height: 500 // can also be excluded
                },
                title: { text: null },
                pane: { size: '95%' },
                legend: {
                    align: 'center',
                    verticalAlign: 'bottom',
                    // y: 100,
                    layout: 'vertical'
                },
                credits: { enabled: false },    // This disables the highcharts watermark.
                xAxis: {
                    min: 0,
                    max: 360,
                    type: "",
                    tickInterval: tickSize,
                    tickmarkPlacement: 'on',
                    labels: {
                        enabled: true, // Otherwise north is hidden...
                        formatter: function () {
                            return categories[this.value / tickSize]; // + '�'; // Index of categories array
                        }
                    }
                },
                yAxis: {
                    min: 0,
                    endOnTick: false,
                    showLastLabel: true,
                    title: {
                        text: null
                    },
                    labels: {
                        enabled: false,
                        formatter: function () {
                            return this.value + '%';
                        }
                    },
                    reversedStacks: false
                },
                tooltip: {
                    // valueSuffix: '%'
                    // shared: true,
                    formatter: function () {
                        // Want both area and percentage to display in tooltip.
                        let percentage = `<span style="font-weight: bold">${this.y.toFixed(2)}%</span>`;
                        let area = '';
                        if (vm.windRoseChartView === 'ExteriorWall')
                            area = `(<span>${vm.wallAreaDataLookup[this.x].toFixed(2)}m&sup2;</span>)`;
                        else if (vm.windRoseChartView === 'ExteriorGlazing')
                            area = `(<span>${vm.glazingAreaDataLookup[this.x].toFixed(2)}m&sup2;</span>)`;
                        else if (vm.windRoseChartView === 'GlassExteriorWallRatio') {
                            let glazing = `(Exterior Wall Area: ${vm.wallAreaDataLookup[this.x].toFixed(2)}m&sup2;)`;
                            let wall = `(Exterior Glazing Area: ${vm.glazingAreaDataLookup[this.x].toFixed(2)}m&sup2;)`;
                            area = `<br><span>${glazing}</span><br><span>${wall}</span>`;
                        }

                        // NOTE: Apparently highcharts does not like it when you have multiple lines using ``'s
                        let tooltip = `<span style="font-size: 11px">${categories[this.x / 45]}</span><br/><span style="color: ${this.color};">\u25CF&nbsp;</span>${this.series.name}: ${percentage} ${area}`;
                        return tooltip;
                    }
                },
                plotOptions: {
                    series: {
                        stacking: 'normal',
                        shadow: false,
                        groupPadding: 0,
                        pointPlacement: 'on'
                    }
                }
            });
        }

        // Finally, initialize our component.
        initialize();

    }
})();