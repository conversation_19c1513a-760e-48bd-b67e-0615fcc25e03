<style>

    .no-tab-bg > md-tabs-wrapper > md-tabs-canvas,
    .no-tab-bg > md-tabs-wrapper > md-tabs-canvas > md-tab-item {
        background-color: transparent;
    }

    .graph-card md-tab-content {
        width: fit-content;
        overflow: hidden;
    }

</style>

<form name="assessmentform" class="main-content-wrapper assessment-content" novalidate data-ng-controller='AssessmentUpdateCtrl as vm' form-error-check>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-subtitle="{{vm.assessment.job.jobReference + ' | ' + vm.assessment.job.clientName + (vm.assessment.assessmentProjectDetail.customClientName != null ? ' (' + vm.assessment.assessmentProjectDetail.customClientName + ')' : '') + ' | ' + vm.assessment.assessmentProjectDetail.clientJobNumber + ' | ' + (vm.assessment.assessmentProjectDetail.useCustomAddress ? vm.assessment.assessmentProjectDetail.customDisplayAddress.replace('\n',', ') : vm.assessment.assessmentProjectDetail.fullAddress) + ' | ' + vm.assessment.assessmentProjectDetail.projectOwner}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-action-bar
             data-quick-find-model=''
             data-action-buttons='vm.actionButtons'
             data-refresh-list=''
             data-spinner-busy='vm.isBusy'
             data-new-record=""
             data-new-record-text=""
             data-is-modal="vm.isModal"
             data-hide="vm.hideActionBar"
             data-dropdown-list="vm.assessmentList"
             data-dropdown-list-model="vm.selectedAssessment"
             data-dropdown-list-display="displayString"
             data-dropdown-list-show="true">
        </div>
        <div data-cc-widget-content
             data-is-modal="vm.isModal">

            <md-tabs md-dynamic-height>

                <!-- Assessment (Edit Assessment) -->
                <md-tab layout="column"
                        ng-if="vm.permission_tab_assessment_view"
                        ng-class="{'tab-has-errors' : assessmentinfoform.$invalid==true}">
                    <md-tab-label>
                        <span>
                            Assessment &nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="assessmentform.assessmentinfoform.$invalid==true">
                                <md-tooltip md-direction="top">
                                    {{vm.userFriendlyErrorMessage(assessmentform.assessmentinfoform.$error)}}
                                </md-tooltip>

                            </i>
                        </span>

                    </md-tab-label>
                    <md-tab-body>
                        <fieldset ng-if="vm.assessment.assessmentProjectDetail != null"
                                  id="template-information-tab-fieldset"
                                  ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit"
                                  ng-form="assessmentinfoform">
                            <md-card>
                                <md-card-header>
                                    <span class="md-headline" ng-class="{'card-has-errors' : assessmentinfoform.$invalid==true}">Assessment Information</span>
                                </md-card-header>
                                <md-card-content>
                                    <div style="display: grid; grid-template-columns: auto 1fr 1fr auto auto;" flex="50">

                                        <!-- ******** Job Reference ******** -->
                                        <md-input-container class="md-block readonly-data" style="margin-top: 13px;">
                                            <label>Job Reference</label>
                                            <span class="read-only-field-value"
                                                  style="min-width: 100px;">
                                                {{vm.assessment.job.jobReference}}
                                            </span>
                                        </md-input-container>

                                        <!-- ******** Status ******** -->
                                        <md-input-container class="md-block readonly-data" style="margin-top: 13px;">
                                            <label>Status</label>
                                            <span ng-if="vm.assessment.job.statusCode == 'JCancelled'"
                                                  style="color:red">Cancelled</span>
                                            <span ng-if="vm.assessment.job.statusCode != 'JCancelled' && vm.newRecord==false"
                                                  class="read-only-field-value"
                                                  ng-class="{'label-red' : vm.assessment.statusCode=='ACancelled',
                                                             'label-green' : vm.assessment.statusCode=='AIssued' ||
                                                                    vm.assessment.statusCode=='AComplete' ||
                                                                    vm.assessment.statusCode=='ACompliance',
                                                            'label-orange' : vm.assessment.statusCode=='ADraft' ||
                                                                    vm.assessment.statusCode=='AInProgress'}">
                                                {{vm.assessment.statusDescription }}
                                            </span>
                                        </md-input-container>

                                        <!-- ******** Creator ******** -->
                                        <md-autocomplete ng-if="vm.permission_field_creator_view && vm.newRecord == false"
                                                         ng-disabled="vm.permission_field_creator_edit == false"
                                                         md-input-name="OrderedBy"
                                                         class="md-block"
                                                         required
                                                         ng-required="true"
                                                         md-input-minlength="2"
                                                         md-min-length="0"
                                                         md-selected-item="vm.assessment.assessmentProjectDetail.creator"
                                                         md-search-text="vm.contactIdSearchText"
                                                         md-items="item in vm.getEmployeesAndClientUsers(vm.contactIdSearchText)"
                                                         md-item-text="item.fullName"
                                                         md-require-match
                                                         md-no-cache="true"
                                                         md-floating-label="Creator">
                                            <md-item-template>
                                                <span md-highlight-text="vm.contactIdSearchText">{{item.fullName}}</span>
                                            </md-item-template>
                                            <div ng-messages="assessmentinfoform.contactId.$error">
                                                <div ng-message="required">Contact is required.</div>
                                            </div>
                                        </md-autocomplete>

                                        <!-- ******** Created ******** -->
                                        <md-input-container class="md-block">
                                            <label>Created</label>
                                            <md-datepicker ng-model="vm.assessment.assessmentProjectDetail.orderDate"
                                                           name="orderDate"
                                                           ng-required="true"
                                                           md-placeholder="Enter date"
                                                           ng-disabled="vm.isLocked">
                                            </md-datepicker>
                                            <div class="validation-messages" ng-messages="assessmentinfoform.orderDate.$error">
                                                <div ng-message="valid">The entered value is not a date!</div>
                                                <div ng-message="required">This date is required!</div>
                                                <div ng-message="mindate">Date is too early!</div>
                                                <div ng-message="maxdate">Date is too late!</div>
                                                <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                                            </div>
                                        </md-input-container>

                                        <!-- ******** Certificate Date ******** -->
                                        <md-input-container class="md-block">
                                            <label>Certificate Date</label>
                                            <md-datepicker ng-model="vm.assessment.certificateDateOverride"
                                                           name="certificateDateOverride"
                                                           md-placeholder="Automatic">
                                            </md-datepicker>
                                            <div class="validation-messages" ng-messages="assessmentinfoform.certificateDateOverride.$error">
                                                <div ng-message="valid">The entered value is not a date!</div>
                                                <div ng-message="required">This date is required!</div>
                                            </div>
                                        </md-input-container>

                                    </div>

                                    <div flex="100" flex-gt-sm="50" layout layout-wrap>

                                        <!-- ******** Client ******** -->
                                        <md-autocomplete flex="70"
                                                         layout=column
                                                         md-input-name="clientId" md-autofocus
                                                         ng-required="true"
                                                         md-input-minlength="2"
                                                         md-min-length="0"
                                                         md-selected-item="vm.assessment.job.client"
                                                         md-search-text="vm.clientIdSearchText"
                                                         md-items="item in vm.getclients(vm.clientIdSearchText)"
                                                         md-item-text="item.clientName"
                                                         md-require-match
                                                         md-floating-label="Client"
                                                         md-selected-item-change="vm.clientChanged()"
                                                         class="md-block">
                                            <md-item-template>
                                                <span md-highlight-text="vm.clientIdSearchText">{{item.clientName}}</span>
                                            </md-item-template>
                                            <div ng-messages="assessmentinfoform.clientId.$error">
                                                <div ng-message="required">Client is required.</div>
                                            </div>
                                            <div ></div>
                                        </md-autocomplete>

                                        <!-- Custom Client Name Checkbox -->
                                        <md-input-container layout="column"
                                                            flex="25"
                                                            class="md-block"
                                                            style="margin: 0 0 auto 20px;">
                                            <md-checkbox ng-model="vm.assessment.assessmentProjectDetail.useCustomClientName"
                                                         ng-change="vm.useCustomClientNameChanged(vm.assessment.assessmentProjectDetail.useCustomClientName)"
                                                         name="useCustomClientName"
                                                         ng-disabled="vm.isLocked">
                                                Custom Client Name
                                            </md-checkbox>
                                        </md-input-container>

                                    </div>

                                    <!-- ******** Custom Client Name ******** -->
                                    <md-input-container ng-if="vm.assessment.assessmentProjectDetail.useCustomClientName" class="md-block" flex="100" flex-gt-sm="50">
                                        <label>Custom Client Name</label>
                                        <input type="text" name="customClientName"
                                               ng-model="vm.assessment.assessmentProjectDetail.customClientName"
                                               ng-required="true" />
                                        <div ng-messages="assessmentinfoform.customClientName.$error">
                                            <div ng-message="required">Custom Client Name is required.</div>
                                        </div>
                                    </md-input-container>

                                    <!-- ******** Client Assignee ******** -->
                                    <md-autocomplete ng-if="vm.permission_field_assignee_view"
                                                     ng-disabled="vm.permission_field_assignee_edit == false"
                                                     class="md-block" flex="100" flex-gt-sm="50"
                                                     ng-required="true"
                                                     md-input-minlength="2"
                                                     md-min-length="0"
                                                     md-selected-item="vm.assessment.assessmentProjectDetail.clientAssignee"
                                                     md-search-text="vm.assigneeIdSearchText"
                                                     md-items="item in vm.getcontacts(vm.assigneeIdSearchText)"
                                                     md-item-text="item.fullName"
                                                     md-require-match
                                                     md-no-cache="true"
                                                     md-floating-label="Assignee">
                                        <md-item-template>
                                            <span md-highlight-text="vm.assigneeIdSearchText">{{item.fullName}}</span>
                                        </md-item-template>
                                        <div ng-messages="assessmentinfoform.contactId.$error">
                                            <div ng-message="required">Contact is required.</div>
                                        </div>
                                    </md-autocomplete>

                                    <!-- ******** Client Job Number ******** -->
                                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                                        <label>Client Job Number</label>
                                        <input type="text" name="clientJobNumber"
                                               ng-model="vm.assessment.assessmentProjectDetail.clientJobNumber"
                                               ng-maxlength="100"
                                               ng-required="true"
                                               ng-blur="vm.clientJobNumberChanged()" />
                                        <div ng-messages="assessmentinfoform.clientJobNumber.$error">
                                            <div ng-message="required">Client Job Number is required.</div>
                                            <div ng-message="maxlength">Too many characters entered, max length is 100.</div>
                                        </div>
                                    </md-input-container>

                                    <!-- Works Description -->
                                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                                        <label>Works Description</label>
                                        <md-select name="WorksDescription"
                                                   ng-required="true"
                                                   ng-model="vm.assessment.worksDescription"
                                                   ng-model-options="{trackBy: '$value.worksDescriptionCode'}"
                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit">
                                            <md-option ng-value="item"
                                                       ng-repeat="item in vm.worksDescriptionList track by item.worksDescriptionCode">
                                                {{item.description}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- ******** Nominated Building Surveyor ******** -->
                                    <md-input-container class="md-block"
                                                        flex="100" flex-gt-sm="50">
                                        <label>Nominated Building Surveyor</label>
                                        <md-select name="nominatedBuildingSurveyor"
                                                   ng-required="true"
                                                   ng-model="vm.assessment.assessmentProjectDetail.nominatedBuildingSurveyorId"
                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit">
                                            <md-option ng-value="item.buildingSurveyorId"
                                                       ng-repeat="item in vm.buildingSurveyorList track by item.buildingSurveyorId">
                                                {{item.description}}
                                            </md-option>
                                        </md-select>
                                        <div ng-messages="assessmentinfoform.nominatedBuildingSurveyor.$error">
                                            <div ng-message="required">Nominated Building Surveyor is required.</div>
                                        </div>
                                    </md-input-container>

                                    <!-- ******** Assessment Version ******** -->
                                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                                        <label>Assessment Version</label>
                                        <input type="text" name="assessmentVersion"
                                               ng-model="vm.assessment.assessmentProjectDetail.assessmentVersion"
                                               ng-required="true"
                                               ng-pattern="vm.assessmentVersionRegex" />
                                        <div ng-messages="assessmentinfoform.assessmentVersion.$error">
                                            <div ng-message="required">Assessment Version is required.</div>
                                            <div ng-message="pattern">Valid format is 1.00</div>
                                        </div>
                                    </md-input-container>

                                    <!-- ******** QR Code on Stamp ******** -->
                                    <md-input-container class="md-block md-input-has-value" flex-gt-sm>
                                        <label>QR Code on Stamp</label>
                                        <md-radio-group ng-model="vm.assessment.qrCodeEnabled"
                                                        layout="row"
                                                        name="qrCodeEnabled"
                                                        class="checkbox-radio-padding"
                                                        ng-disabled="vm.allowQRCodeChoice == false || vm.isLocked">
                                            <md-radio-button ng-value="true">Yes</md-radio-button>
                                            <md-radio-button ng-value="false">No</md-radio-button>
                                        </md-radio-group>
                                    </md-input-container>

                                    <!-- ******** Assigned To Assessor Employee ******** -->
                                    <md-autocomplete ng-if="vm.permission_field_assignedassessor_view"
                                                     ng-disabled="vm.permission_field_assignedassessor_edit == false"
                                                     md-input-name="Assigned Assessor"
                                                     class="md-block"
                                                     flex="100"
                                                     flex-gt-sm="50"
                                                     md-input-minlength="2"
                                                     md-min-length="0"
                                                     md-selected-item="vm.assessment.assessorUser"
                                                     md-search-text="vm.assignedToAssessorUserIdSearchText"
                                                     md-items="item in vm.getemployees(vm.assignedToAssessorUserIdSearchText)"
                                                     md-item-text="item.fullName"
                                                     md-require-match
                                                     required
                                                     ng-required="true"
                                                     md-floating-label="Assigned Assessor">
                                        <md-item-template>
                                            <span md-highlight-text="vm.assignedToAssessorUserIdSearchText">{{item.fullName}}</span>
                                        </md-item-template>
                                    </md-autocomplete>

                                    <!-- ******** Priority ******** -->
                                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                                        <label>Priority</label>
                                        <md-select name="priority"
                                                   ng-model="vm.assessment.priorityCode"
                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit">
                                            <md-option ng-value="item.priorityCode"
                                                       ng-repeat="item in vm.priorityList track by item.priorityCode">
                                                {{item.description}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Design Changes -->
                                    <md-input-container ng-if="vm.assessment.assessmentProjectDetail.isRecertified"
                                                        class="md-block"
                                                        flex="100"
                                                        flex-gt-sm="50">
                                        <label>Design Changes</label>
                                        <md-select name="designChanges"
                                                   ng-required="true"
                                                   ng-model="vm.assessment.recertificationDesignChanges"
                                                   multiple="true">
                                            <md-option ng-value="item.designChangeId"
                                                       ng-repeat="item in vm.designChangeList">
                                                {{item.description}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- ******** Notes ******** -->
                                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                                        <label>Notes For Assessor</label>
                                        <textarea type="text" name="notes"
                                                  ng-model="vm.assessment.notes"
                                                  rows="3" />
                                        <div ng-messages="assessmentinfoform.notes.$error">
                                            <div ng-message="required">Notes are required.</div>
                                        </div>
                                    </md-input-container>

                                </md-card-content>
                            </md-card>
                            <md-card ng-if="vm.permission_field_eventhistory_view">
                                <md-card-header>
                                    <span class="md-headline">Event History</span>
                                </md-card-header>
                                <md-card-content>
                                    <div class="event" ng-repeat="event in vm.assessment.job.jobEvents track by $index">
                                        {{event.description}} at <span>{{event.createdOn | date: 'dd/MM/yyyy hh:mm a'}}</span> by <span>{{event.createdByName}}</span>
                                    </div>
                                </md-card-content>
                            </md-card>
                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- Invoicing -->
                <md-tab layout="column"
                        ng-if="vm.permission_tab_invoicing_view">
                    <md-tab-label>
                        <span>
                            Invoicing&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="assessmentform.invoicingForm.$invalid">
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>
                        <fieldset ng-if="vm.assessment.assessmentProjectDetail != null"
                                  id="billing-details-tab-fieldset"
                                  redi-enable-roles="assessment_page_(tabs/sub-tabs)__invoicing__edit"
                                  ng-disabled="(vm.isLocked)"
                                  ng-form="invoicingForm">

                            <md-card ng-if="vm.assessment.job.client.clientDefault.purchaseOrderCode == 'File' ||
                                            vm.assessment.job.client.clientDefault.purchaseOrderCode == 'NumberAndFile' ||
                                            vm.assessment.job.client.clientDefault.purchaseOrderCode == 'Number'">
                                <md-card-header>
                                    <span class="md-headline">
                                        Purchase Orders
                                    </span>
                                </md-card-header>

                                <md-card-content style="margin-top: 0px; padding-top: 0px;">

                                    <!-- Always-present Baseline Purchase order settings. -->
                                    <md-card ng-form="purchaseOrderBaselineForm">
                                        <md-card-header>
                                            <span class="md-subhead"
                                                  ng-class="{'card-has-errors' : purchaseOrderBaselineForm.$invalid==true}">
                                                Baseline
                                            </span>
                                        </md-card-header>
                                        <md-card-content flex="100" flex-gt-sm="50">

                                            <!-- ******** Creator (READ ONLY) ******** -->
                                            <md-input-container class="md-block">
                                                <label>Creator</label>
                                                <input type="text"
                                                       name="orderedBy"
                                                       ng-model="vm.assessment.allComplianceOptions[0].createdByName"
                                                       ng-disabled="true"
                                                       ng-maxlength="200" />
                                            </md-input-container>

                                            <!-- ******** Ordered Date (READ ONLY) ******** -->
                                            <md-input-container class="md-block">
                                                <label>Created</label>
                                                <input type="text"
                                                       name="orderDate"
                                                       ng-model="vm.assessment.allComplianceOptions[0].createdOn"
                                                       ng-disabled="true"
                                                       ng-maxlength="200" />
                                            </md-input-container>

                                            <!-- ******** Assessment Method (READ ONLY) ******** -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Baseline Assessment Method</label>
                                                <md-select name="preliminaryComplianceMethodCode"
                                                           ng-model="vm.assessment.allComplianceOptions[0].complianceMethodCode"
                                                           ng-disabled="true"
                                                           ng-change="vm.validatePreliminaryComplianceMethodChange()">
                                                    <md-option ng-value="item.complianceMethodCode"
                                                               ng-repeat="item in vm.getComplianceMethodList()  track by item.complianceMethodCode">
                                                        {{item.description}}
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- ******** Purchase Order ******** -->
                                            <md-input-container class="md-block"
                                                                ng-if="vm.assessment.job.client.clientDefault.purchaseOrderCode=='NumberAndFile' || vm.assessment.job.client.clientDefault.purchaseOrderCode=='Number'">
                                                <label>Purchase Order</label>
                                                <input type="text" name="purchaseOrder"
                                                       ng-disabled="vm.isLocked"
                                                       ng-model="vm.assessment.allComplianceOptions[0].purchaseOrder"
                                                       ng-required="true"
                                                       ng-maxlength="200" />
                                                <div ng-messages="purchaseOrderBaselineForm.purchaseOrder.$error">
                                                    <div ng-message="required">Purchase Order is required.</div>
                                                </div>
                                            </md-input-container>
                                            <div ng-if="vm.assessment.job.client.clientDefault.purchaseOrderCode=='NumberAndFile' || vm.assessment.job.client.clientDefault.purchaseOrderCode=='File'">
                                                <label style="font-size: 9px; color: gray;">Purchase Order File</label>
                                                <div class="md-block" flex="100" flex-gt-sm="50">
                                                    <file-upload prop-name="purchaseOrderFile"
                                                                 is-locked="vm.isLocked || vm.permission_tab_invoicing_edit== false"
                                                                 job-files="vm.jobFiles"
                                                                 is-required="true"
                                                                 required-message="''"
                                                                 assessment="vm.assessment"
                                                                 job="vm.assessment.job"
                                                                 category="'Invoicing'"
                                                                 classification="'Purchase Order File'"
                                                                 file-object="vm.assessment.allComplianceOptions[0]"></file-upload>
                                                </div>
                                            </div>
                                        </md-card-content>
                                    </md-card>

                                    <!-- If required, show any selected optional simulations purchase order info-->
                                    <div ng-repeat="option in vm.assessment.allComplianceOptions"
                                         lr-drag-src="complianceOptionsList"
                                         lr-drop-target="complianceOptionsList"
                                         lr-drag-data="vm.assessment.allComplianceOptions"
                                         lr-drop-success="vm.reorderComplianceOptions(item)"
                                         lr-match-property="optionIndex"
                                         lr-match-value="{{option.optionIndex}}"
                                         lr-index="vm.assessment.allComplianceOptions.indexOf(option)"
                                         style="margin-bottom: 20px;">

                                        <md-card ng-if="option.isSelected && !option.isBaselineSimulation"
                                                 ng-form="purchaseOrderOptionForm">
                                            <md-card-header>
                                                <span class="md-subhead"
                                                      ng-class="{'card-has-errors' : purchaseOrderOptionForm.$invalid==true}">
                                                    Option {{option.optionIndex}}
                                                </span>
                                            </md-card-header>

                                            <md-card-content flex="100" flex-gt-sm="50">

                                                <!-- ******** Creator (READ ONLY) ******** -->
                                                <md-input-container class="md-block">
                                                    <label>Creator</label>
                                                    <input type="text"
                                                           name="orderedBy"
                                                           ng-model="option.modifiedByName"
                                                           ng-disabled="true"
                                                           ng-maxlength="200" />
                                                </md-input-container>

                                                <!-- ******** Ordered Date (READ ONLY) ******** -->
                                                <md-input-container class="md-block">
                                                    <label>Created</label>
                                                    <input type="text"
                                                           name="orderDate"
                                                           ng-model="option.createdOn"
                                                           ng-disabled="true"
                                                           ng-maxlength="200" />
                                                </md-input-container>

                                                <!-- ******** Assessment Method (READ ONLY) ******** -->
                                                <md-input-container class="md-block" flex="100">
                                                    <label>Compliance Method</label>
                                                    <md-select name="preliminaryComplianceMethodCode"
                                                               ng-model="option.complianceMethod.complianceMethodCode"
                                                               ng-disabled="true">
                                                        <md-option ng-value="item.complianceMethodCode"
                                                                   ng-repeat="item in vm.getComplianceMethodList()  track by item.complianceMethodCode">
                                                            {{item.description}}
                                                        </md-option>
                                                    </md-select>
                                                </md-input-container>

                                                <!-- Purchase Order Form (EDITABLE) -->
                                                <div ng-if="option.isSelected===true && option.newPurchaseOrderRequired == true">

                                                    <!-- Purchase Order # (EDITABLE) -->
                                                    <md-input-container class="md-block"
                                                                        ng-if="vm.assessment.job.client.clientDefault.purchaseOrderCode=='NumberAndFile' || vm.assessment.job.client.clientDefault.purchaseOrderCode=='Number'">
                                                        <label>Purchase Order</label>
                                                        <input type="text" name="purchaseOrder"
                                                               ng-model="option.purchaseOrder"
                                                               ng-required="true"
                                                               ng-maxlength="200" />
                                                        <div ng-messages="purchaseOrderOptionForm.purchaseOrder.$error">
                                                            <div ng-message="required">Purchase Order is required.</div>
                                                        </div>
                                                    </md-input-container>

                                                    <!-- Purchase Order File (EDITABLE) -->
                                                    <div ng-if="vm.assessment.job.client.clientDefault.purchaseOrderCode=='NumberAndFile' || vm.assessment.job.client.clientDefault.purchaseOrderCode=='File'">
                                                        <label style="font-size: 9px; color: gray;">Purchase Order File</label>
                                                        <file-upload class="vertically-condensed vertically-condensed-ex"
                                                                     is-locked="vm.isLocked || vm.permission_tab_invoicing_edit== false"
                                                                     label="Purchase Order File"
                                                                     assessment="vm.assessment"
                                                                     job="vm.assessment.job"
                                                                     category="'Invoicing'"
                                                                     classification="'Purchase Order File'"
                                                                     file-object="option"
                                                                     prop-name="purchaseOrderFile"
                                                                     is-required="true"
                                                                     required-message="''">
                                                        </file-upload>
                                                    </div>

                                                </div>

                                            </md-card-content>

                                        </md-card>

                                    </div>

                                </md-card-content>
                            </md-card>

                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- Site -->
                <md-tab label="Site" layout="column"
                        ng-if="vm.permission_tab_site_view"
                        ng-class="{'tab-has-errors' : assessmentform.buildingSiteForm.$invalid==true}">

                    <md-tab-label>
                        <span>
                            Site&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange" aria-hidden="true"
                               ng-if="assessmentform.buildingSiteForm.$invalid || vm.cachedMapImagesNotCaptured">
                                <md-tooltip md-direction="top">
                                    {{vm.cachedBuildingSiteFormErrors}} {{vm.cachedMapImagesNotCaptured}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>

                    <md-tab-body>

                        <md-tabs ng-form="buildingSiteForm"
                                 redi-enable-roles="assessment_page_(tabs/sub-tabs)__site__edit">

                            <!-- Address Tab -->
                            <md-tab ng-form="addressForm" ng-if="vm.permission_tab_address_view">
                                <md-tab-label>
                                    Address&nbsp;
                                    <i class="fa fa-exclamation-triangle label-orange" aria-hidden="true"
                                       ng-if="assessmentform.buildingSiteForm.addressForm.$invalid">
                                        <md-tooltip md-direction="top">
                                            {{vm.cachedAddressFormErrors}}
                                        </md-tooltip>
                                    </i>
                                </md-tab-label>
                                <md-tab-body>
                                    <fieldset ng-if="vm.assessment.assessmentProjectDetail != null"
                                              redi-enable-roles="assessment_page_(tabs/sub-tabs)__address__edit"
                                              id="site-info-map-fieldset" ng-disabled="(vm.isLocked)">
                                        <md-card ng-form="addressForm">
                                            <md-card-content layout="column"
                                                             layout-gt-md="row">

                                                <!-- Site Information Form-style Inputs -->
                                                <div flex="100" flex-gt-md="50">

                                                    <div style="padding: 10px 0 30px 0;">
                                                        <md-radio-group layout="row"
                                                                        ng-model="vm.assessment.assessmentProjectDetail.isManual"
                                                                        ng-change="vm.clearAddress()"
                                                                        ng-disabled="vm.isLocked">
                                                            <md-radio-button ng-value="false">
                                                                Search Address
                                                            </md-radio-button>
                                                            <md-radio-button ng-value="true">
                                                                Manual Address Entry
                                                            </md-radio-button>

                                                        </md-radio-group>
                                                    </div>

                                                    <!-- Auto Search Address -->
                                                    <div ng-if="!vm.assessment.assessmentProjectDetail.isManual">
                                                        <search-address ng-if="vm.permission_field_searchaddress_view"
                                                                        is-disabled="vm.permission_field_searchaddress_edit == false"
                                                                        assessment="vm.assessment"
                                                                        address-changed="vm.addressChangedViaSearchAddress(); vm.useCustomAddressChanged(false, false);"
                                                                        on-manual-address-selected="vm.clearAddress()">
                                                        </search-address>
                                                        <md-input-container class="md-block" flex="100">
                                                            <label>Lot No.</label>
                                                            <input ng-change="vm.addressChanged()"
                                                                   type="text"
                                                                   name="lotNumber"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.lotNumber" />
                                                        </md-input-container>
                                                    </div>

                                                    <!-- Manual address entry -->
                                                    <div ng-if="vm.assessment.assessmentProjectDetail.isManual">

                                                        <!-- Lot Type -->
                                                        <md-input-container class="md-block" flex="100">
                                                            <label>Lot Type</label>
                                                            <md-select name="lotType"
                                                                       ng-change="vm.addressChanged()"
                                                                       ng-model="vm.assessment.assessmentProjectDetail.lotType"
                                                                       ng-model-options="{trackBy: '$value.lotTypeCode'}"
                                                                       ng-disabled="vm.isLocked">
                                                                <md-option ng-repeat="lotType in vm.lotTypeList track by $index"
                                                                           ng-value="lotType">
                                                                    {{lotType.description}}
                                                                </md-option>
                                                            </md-select>
                                                        </md-input-container>

                                                        <!-- Lot No -->
                                                        <md-input-container class="md-block" flex="100">
                                                            <label>{{vm.assessment.assessmentProjectDetail.lotType.description || 'Lot' }} No.</label>
                                                            <input ng-change="vm.addressChanged()" type="text" name="lotNumber" ng-model="vm.assessment.assessmentProjectDetail.lotNumber" />
                                                        </md-input-container>

                                                        <!-- Street Number -->
                                                        <md-input-container class="md-block" flex-gt-sm="100">
                                                            <label>Street Number</label>
                                                            <input type="text"
                                                                   name="houseNumber"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.houseNumber"
                                                                   ng-maxlength="20"
                                                                   ng-change="vm.addressChanged()" />
                                                            <div ng-messages="assessmentform.buildingSiteForm.addressForm.houseNumber.$error">
                                                                <div ng-message="maxlength">Too many characters entered, max length is 20.</div>
                                                            </div>
                                                        </md-input-container>

                                                        <!-- Street Name -->
                                                        <md-input-container class="md-block" flex-gt-sm="100">
                                                            <label>Street Name</label>
                                                            <input type="text" name="streetName"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.streetName"
                                                                   ng-maxlength="400"
                                                                   ng-change="vm.addressChanged()" />
                                                            <div ng-messages="assessmentform.buildingSiteForm.addressForm.streetName.$error">
                                                                <div ng-message="maxlength">Too many characters entered, max length is 400.</div>
                                                            </div>
                                                        </md-input-container>

                                                        <!-- Street Type -->
                                                        <search-street-type assessment="vm.assessment" address-changed="vm.addressChanged()"></search-street-type>

                                                        <!-- Suburb -->
                                                        <search-suburb initial-suburb-text="vm.assessment.suburb"
                                                                       selected-suburb="vm.selectedSuburb"
                                                                       suburb-changed="vm.suburbChanged();">
                                                        </search-suburb>

                                                        <!-- State -->
                                                        <md-input-container class="md-block" flex="100">
                                                            <label>State</label>
                                                            <input ng-disabled="true"
                                                                   type="text"
                                                                   name="stateCode"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.stateCode" />
                                                        </md-input-container>

                                                        <!-- Postcode -->
                                                        <md-input-container class="md-block" flex="100">
                                                            <label>Postcode</label>
                                                            <input ng-disabled="true"
                                                                   type="text"
                                                                   name="postcode"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.postcode" />
                                                        </md-input-container>

                                                    </div>

                                                    <!-- Display Project Address + Custom Address Checkbox + Custom Address -->
                                                    <div flex="100" layout layout-wrap>

                                                        <!-- Display project address + Custom Address Checkbox -->
                                                        <div layout=column flex="65">

                                                            <!-- Display Project Address -->
                                                            <div flex="65" layout layout-wrap>
                                                                <div flex="100" layout layout-align="start center">
                                                                    <span>Display Project Address</span>
                                                                </div>
                                                                <div flex="100" layout>
                                                                    <md-input-container class="md-block" flex="100">
                                                                        <textarea ng-model="vm.assessment.assessmentProjectDetail.originalDisplayAddress" readonly></textarea>
                                                                    </md-input-container>
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <!-- Custom Address Checkbox -->
                                                        <md-input-container ng-if="vm.permission_field_customaddress_view"
                                                                            layout="column"
                                                                            flex="35"
                                                                            class="md-block"
                                                                            style="margin: 0 0 auto auto;">
                                                            <md-checkbox ng-model="vm.assessment.assessmentProjectDetail.useCustomAddress"
                                                                         ng-change="vm.useCustomAddressChanged(vm.assessment.assessmentProjectDetail.useCustomAddress)"
                                                                         name="useCustomAddress"
                                                                         ng-disabled="vm.isLocked || vm.permission_field_customaddress_edit == false">
                                                                Custom project address
                                                            </md-checkbox>
                                                        </md-input-container>

                                                    </div>

                                                    <!--custom project addresss-->
                                                    <div flex="100" layout layout-wrap ng-if="vm.assessment.assessmentProjectDetail.useCustomAddress">
                                                        <div flex="100" layout layout-align="start center">
                                                            <span>Custom Project Address</span>
                                                        </div>
                                                        <div flex="100" layout>
                                                            <md-input-container class="md-block" flex="100">
                                                                    <textarea ng-model="vm.assessment.assessmentProjectDetail.customDisplayAddress"
                                                                              style="overflow: hidden;"
                                                                              name="customDisplayAddress"
                                                                              rows="2" maxlength="200"
                                                                              max-rows="2"
                                                                              maxLines="2"
                                                                              required
                                                                              maxlines-prevent-enter="true"></textarea>
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.customDisplayAddress.$error">
                                                                    <div ng-message="required">Custom Address is required.</div>
                                                                    <div ng-message="maxlength">Too many characters entered, max length is 100.</div>
                                                                    <div ng-message="maxlines">Too many lines. Max 2 lines.</div>
                                                                </div>
                                                            </md-input-container>
                                                        </div>
                                                    </div>

                                                    <!-- ******** Project Owner ******** -->
                                                    <md-input-container class="md-block" flex="100" ng-if="vm.permission_field_projectowner_view"">
                                                        <label>Project Owner</label>
                                                        <input type="text" name="projectOwner"
                                                               ng-disabled="vm.permission_field_projectowner_edit == false"
                                                               ng-model="vm.assessment.assessmentProjectDetail.projectOwner"
                                                               ng-maxlength="1000"
                                                               ng-required="true" />
                                                        <div ng-messages="assessmentinfoform.projectOwner.$error">
                                                            <div ng-message="required">Project Owner is required.</div>
                                                            <div ng-message="maxlength">Too many characters entered, max length is 1000.</div>
                                                        </div>
                                                    </md-input-container>

                                                    <div layout="row" style="padding-bottom: 18px;">
                                                        <span style="margin: auto 0px;"><strong>Coordinates</strong></span>
                                                        <div layout="row"
                                                             class="my-auto"
                                                             style="margin: auto 0px;"
                                                             ng-if="vm.assessment.assessmentProjectDetail.longitude && vm.assessment.assessmentProjectDetail.latitude">

                                                            <a ng-href="https://maps.google.com/?q={{vm.assessment.assessmentProjectDetail.latitude}},{{vm.assessment.assessmentProjectDetail.longitude}}"
                                                               target="_blank">
                                                                <i class="fa fa-map" style="padding-left: 10px; color: #81BEF7;"></i>
                                                                <md-tooltip md-direction="top" style="margin-left: 10px;">
                                                                    Google Maps
                                                                </md-tooltip>
                                                            </a>

                                                            <a ng-href="https://web.metromap.com.au/map?lat={{vm.assessment.assessmentProjectDetail.latitude}}&lng={{vm.assessment.assessmentProjectDetail.longitude}}&zoom=19"
                                                               target="_blank">
                                                                <i class="fa fa-map" style="padding-left: 10px; color: #F78181;"></i>
                                                                <md-tooltip md-direction="top" style="margin-left: 10px;">
                                                                    MetroMaps
                                                                </md-tooltip>
                                                            </a>
                                                        </div>

                                                        <!-- Switch between decimals and degrees/minutes/seconds-->
                                                        <div style="margin: 0px 0px 0px auto;">
                                                            <div layout="row">

                                                                <div ng-if='!vm.useDMS'>
                                                                    <md-button class="md-raised"
                                                                               ng-click="vm.switchToDMSInput()"
                                                                               ng-show="!vm.isLocked"
                                                                               ng-disabled="(vm.isLocked)">
                                                                        DMS
                                                                    </md-button>
                                                                </div>

                                                                <div ng-if='vm.useDMS'>
                                                                    <md-button class="md-raised"
                                                                               ng-click="vm.switchToDecimalInput()"
                                                                               ng-show="!vm.isLocked"
                                                                               ng-disabled="(vm.isLocked)">
                                                                        Decimal Degrees
                                                                    </md-button>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Lat/Long Decimal Input-->
                                                    <div ng-if='!vm.useDMS'>

                                                        <!-- ******** Latitude ******** -->
                                                        <md-input-container dividerColor="primary" class="md-block">
                                                            <label ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? vm.permission_tab_map_edit? 'black' : 'lightgrey' : 'red'}">
                                                                Latitude
                                                            </label>
                                                            <input type="text" name="latitude"
                                                                   ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? vm.permission_tab_map_edit? 'black' : 'lightgrey' : 'red'}"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.latitude"
                                                                   pattern="{{vm.latRegex}}" />
                                                            <div ng-messages="assessmentform.buildingSiteForm.addressForm.latitude.$error">
                                                                <div ng-message="required">Latitude is required.</div>
                                                                <div ng-message="pattern">Incorrect Latitude coordinate</div>
                                                            </div>
                                                        </md-input-container>

                                                        <!-- ******** Longitude ******** -->
                                                        <md-input-container class="md-block">
                                                            <label ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? vm.permission_tab_map_edit? 'black' : 'lightgrey' : 'red'}">
                                                                Longitude
                                                            </label>
                                                            <input type="text" name="longitude"
                                                                   ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? vm.permission_tab_map_edit? 'black' : 'lightgrey' : 'red'}"
                                                                   ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.longitude"
                                                                   pattern="{{vm.lonRegex}}" />
                                                            <div ng-messages="assessmentform.buildingSiteForm.addressForm.longitude.$error">
                                                                <div ng-message="required">Longitude is required.</div>
                                                                <div ng-message="pattern">Incorrect Longitude coordinate</div>
                                                            </div>
                                                        </md-input-container>

                                                    </div>

                                                    <!-- Lat/Long DMS Input -->
                                                    <div ng-if='vm.useDMS'>
                                                        <!-- Latitude DMS -->
                                                        <md-input-container class="md-block" layout-padding>
                                                            <label>Latitude</label>
                                                        </md-input-container>
                                                        <div layout-gt-sm="row">
                                                            <!-- ******** Degrees ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Degrees</label>
                                                                <input type="text" name="latDegrees"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lat.d"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.degreesDMSRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.latDegrees.$error">
                                                                    <div ng-message="required">Degrees is required.</div>
                                                                    <div ng-message="pattern">Valid format is 000</div>
                                                                </div>
                                                            </md-input-container>

                                                            <!-- ******** Minutes ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Minutes</label>
                                                                <input type="text" name="latMinutes"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lat.m"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.minutesDMSRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.latMinutes.$error">
                                                                    <div ng-message="required">Minutes is required.</div>
                                                                    <div ng-message="pattern">Valid format is 00</div>
                                                                </div>
                                                            </md-input-container>

                                                            <!-- ******** Seconds ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Seconds</label>
                                                                <input type="text" name="latSeconds"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lat.s"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.secondsDMSRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.latSeconds.$error">
                                                                    <div ng-message="required">Seconds is required.</div>
                                                                    <div ng-message="pattern">Valid format is 00.00</div>
                                                                </div>
                                                            </md-input-container>

                                                            <!-- ******** Direction ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Direction</label>
                                                                <input type="text" name="latDirection"
                                                                       capitalize
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lat.direction"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.directionLatRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.latDirection.$error">
                                                                    <div ng-message="required">Direction is required.</div>
                                                                    <div ng-message="pattern">Either N, S</div>
                                                                </div>
                                                            </md-input-container>
                                                        </div>

                                                        <!-- Longitude DMS -->
                                                        <md-input-container class="md-block" layout-padding>
                                                            <label>Longitude</label>
                                                        </md-input-container>
                                                        <div layout-gt-sm="row">
                                                            <!--- ******** Degrees ******** --->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Degrees</label>
                                                                <input type="text" name="lonDegrees"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lng.d"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.degreesDMSRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.lonDegrees.$error">
                                                                    <div ng-message="required">Degrees is required.</div>
                                                                    <div ng-message="pattern">Valid format is 000</div>
                                                                </div>
                                                            </md-input-container>

                                                            <!-- ******** Minutes ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Minutes</label>
                                                                <input type="text" name="lonMinutes"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lng.m"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.minutesDMSRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.lonMinutes.$error">
                                                                    <div ng-message="required">Minutes is required.</div>
                                                                    <div ng-message="pattern">Valid format is 00</div>
                                                                </div>
                                                            </md-input-container>

                                                            <!-- ******** Seconds ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Seconds</label>
                                                                <input type="text" name="lonSeconds"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lng.s"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.secondsDMSRegex" />
                                                                <div ng-messages="buildingSiteForm.lonSeconds.$error">
                                                                    <div ng-message="required">Seconds is required.</div>
                                                                    <div ng-message="pattern">Valid format is 00.00</div>
                                                                </div>
                                                            </md-input-container>

                                                            <!-- ******** Direction ******** -->
                                                            <md-input-container class="md-block" flex-gt-sm="25">
                                                                <label>Direction</label>
                                                                <input type="text" name="lonDirection"
                                                                       class="vertically-condensed"
                                                                       ng-model="vm.dms.lng.direction"
                                                                       ng-disabled="vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed"
                                                                       ng-style="{'color': vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed ? 'lightgrey' : 'red'}"
                                                                       ng-pattern="vm.directionLonRegex" />
                                                                <div ng-messages="assessmentform.buildingSiteForm.addressForm.lonDirection.$error">
                                                                    <div ng-message="required">Direction is required.</div>
                                                                    <div ng-message="pattern">Either E, W</div>
                                                                </div>
                                                            </md-input-container>

                                                        </div>
                                                    </div>

                                                    <div>

                                                        <!-- ******** Local Government Authority ******** -->
                                                        <md-input-container flex="100" class="md-block">
                                                            <label>Local Government Authority</label>
                                                            <input type="text" name="localGovernmentAuthority"
                                                                   ng-required="true"
                                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.localGovernmentAuthority"
                                                                   ng-maxlength="100" />
                                                            <div ng-messages="assessmentform.buildingSiteForm.addressForm.localGovernmentAuthority.$error">
                                                                <div ng-message="required">Local Government Authority is required.</div>
                                                                <div ng-message="maxlength">Too many characters entered, max length is 100.</div>
                                                            </div>
                                                        </md-input-container>

                                                        <!-- ******** Plan Type ******** -->
                                                        <md-input-container flex="100" class="md-block">
                                                            <label>Plan Type</label>
                                                            <input type="text" name="PlanType"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.planType"
                                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit"
                                                                   ng-maxlength="100" />
                                                        </md-input-container>

                                                        <!-- ******** Plan Number ******** -->
                                                        <md-input-container class="md-block" flex="100">
                                                            <label>Plan Number</label>
                                                            <input type="text"
                                                                   name="PlanNumber"
                                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.depositedPlanNumber" />
                                                        </md-input-container>

                                                        <!-- ******** Certificate of Title / Volume/Folio ******** -->
                                                        <md-input-container flex="100" class="md-block">
                                                            <label>Volume/Folio</label>
                                                            <input type="text" name="PlanType"
                                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit"
                                                                   ng-model="vm.assessment.assessmentProjectDetail.certificateOfTitle"
                                                                   ng-maxlength="100" />
                                                        </md-input-container>

                                                        <!-- Number of Boundaries / Boundary Sides (moved to new tab) -->

                                                        <!-- Corner Block (moved to new tab) -->

                                                    </div>

                                                    <!-- ******** Building Exposure ******** -->
                                                    <md-input-container class="md-block" flex="100">
                                                        <label>Building Exposure</label>
                                                        <md-select name="buildingExposureCode"
                                                                   ng-model="vm.assessment.buildingExposureCode"
                                                                   ng-disabled="vm.isLocked || !vm.permission_tab_assessment_edit">
                                                            <md-option ng-value="item.buildingExposureCode"
                                                                       ng-repeat="item in vm.buildingExposureList track by item.buildingExposureCode">
                                                                {{item.description}}
                                                            </md-option>
                                                        </md-select>
                                                    </md-input-container>

                                                    <!-- ******** Bushfire Prone ******** -->
                                                    <md-input-container ng-if="vm.assessment.bushFireProneUnknown" class="md-block" flex="100">
                                                        <label>Bushfire Prone</label>
                                                        <md-select name="BushfireProne"
                                                                   ng-model="vm.assessment.isBushFireProne"
                                                                   ng-change="vm.isBushFireProneChange()"
                                                                   ng-required="true"
                                                                   ng-disabled="(vm.isLocked) || !vm.permission_tab_assessment_edit">
                                                            <md-option ng-value="true">
                                                                Yes
                                                            </md-option>
                                                            <md-option ng-value="false">
                                                                No
                                                            </md-option>
                                                        </md-select>
                                                    </md-input-container>

                                                    <!-- ******** Bushfire Attack Level ******** -->
                                                    <md-input-container ng-if="vm.assessment.isBushFireProne" class="md-block" flex="100">
                                                        <label>Bushfire Attack Level</label>
                                                        <md-select name="BushfireAttackLevelCode"
                                                                   ng-model="vm.assessment.bushfireAttackLevelCode"
                                                                   ng-required="true"
                                                                   ng-disabled="(vm.isLocked) || !vm.permission_tab_assessment_edit">
                                                            <md-option ng-value="item.bushfireAttackLevelCode"
                                                                       ng-repeat="item in vm.bushfireAttackLevelList track by item.bushfireAttackLevelCode">
                                                                {{item.description}}
                                                            </md-option>
                                                        </md-select>
                                                    </md-input-container>
                                                </div>

                                                <div style="width: 20px;">
                                                </div>

                                            </md-card-content>

                                        </md-card>

                                    </fieldset>

                                </md-tab-body>
                            </md-tab>

                            <!-- Map Tab -->
                            <md-tab ng-if="vm.permission_tab_map_view">
                                <md-tab-label>
                                    Map
                                    <i class="fa fa-exclamation-triangle label-orange"
                                       aria-hidden="true"
                                       ng-if="!vm.cachedHasSiteMapImageData">
                                        <md-tooltip md-direction="top">
                                            Map image has not been captured.
                                        </md-tooltip>
                                    </i>
                                </md-tab-label>

                                <md-tab-body>

                                    <fieldset id="map-display-fc-1"
                                              redi-enable-roles="assessment_page_(tabs/sub-tabs)__map__edit"
                                              ng-if="vm.assessment.assessmentProjectDetail != null">

                                        <!-- Aerial map to the right -->
                                        <div flex="100"
                                             id="siteinfo_div">

                                            <!-- Switch between showing the raw image or the URL. Raw image takes precedence. -->
                                            <div ng-switch on="vm.siteInfoMap.imageBuffer == null"
                                                 ng-if="vm.cachedHasSiteMapImageData"
                                                 style="position: relative; max-width:100%;">

                                                <img ng-switch-when="false"
                                                     src="{{vm.siteInfoMap.imageBuffer}}"
                                                     style="width: 100%; height: 1300px; object-fit: cover; object-position: 50% 50%;" />

                                                <img ng-switch-default
                                                     src="{{vm.assessment.assessmentProjectDetail.siteMapImageFile.url}}"
                                                     style="width: 100%; height: 1300px; object-fit: cover; object-position: 50% 50%;" />

                                                <img src="/content/images/north-arrow.svg"
                                                     style="position: absolute; right: 20px; bottom: 20px;
                                                        height: 50px;" />
                                            </div>

                                            <!-- Actual Map Div with reset and capture buttons -->
                                            <div id="map-div"
                                                 style="text-align: center;"
                                                 ng-show="!vm.cachedHasSiteMapImageData">

                                                <md-button ng-click="vm.resetMapToDefault(vm.siteInfoMap);"
                                                           ng-disabled="vm.isLocked"
                                                           class="md-raised md-warn">
                                                    Recentre
                                                </md-button>
                                                <md-button ng-click="vm.captureCurrentView(vm.siteInfoMap)"
                                                           ng-disabled="vm.isLocked"
                                                           class="md-raised md-warn">
                                                    Capture
                                                </md-button>
                                                <md-button ng-click="vm.resetToPrePanState();"
                                                           ng-if="vm.assessmentStateBackup != null"
                                                           ng-disabled="vm.isLocked"
                                                           class="md-raised">
                                                    Cancel
                                                </md-button>

                                                <div style="position: relative">
                                                    <div id="siteinfo_map" style="width: 100%; height: 1300px; object-fit: cover;">
                                                        <img src="/content/images/north-arrow.svg"
                                                             style="position: absolute; right: 20px; bottom: 20px;
                                                            height: 50px; z-index: 9999;" />

                                                        <!-- Loading Icon Overlay -->
                                                        <div ng-if="vm.siteInfoMap.isBusy"
                                                             data-cc-spinner="vm.spinnerOptions"
                                                             style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;
                                                                background-color: rgba(255, 255, 255, 0.8); z-index: 999;
                                                                display: flex; flex-direction: column; justify-content: center; align-items: center;" />
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Discard and Checkbox-->
                                            <div ng-if="vm.cachedHasSiteMapImageData"
                                                 style="text-align: center;">
                                                <div>
                                                    <md-checkbox ng-model="vm.assessment.includeMapInExportedPDF"
                                                                 name="includeMapInExportedPDF"
                                                                 ng-disabled="vm.isLocked"
                                                                 style="margin-top: 15px;">
                                                        Include In PDF
                                                    </md-checkbox>
                                                </div>

                                                <div>
                                                    <md-button ng-click="vm.saveCurrentAddressAndMapState(); vm.releaseMapImage(vm.siteInfoMap, 'siteMapImageFile', 15.75)"
                                                               class="md-raised md-warn"
                                                               ng-disabled="vm.isLocked">
                                                        Discard Image
                                                    </md-button>
                                                </div>
                                            </div>

                                        </div>

                                    </fieldset>

                                </md-tab-body>

                            </md-tab>

                            <!-- Aerial Image -->
                            <md-tab layout="column"
                                    ng-if="vm.permission_tab_aerialImage_view">
                                <md-tab-label>
                                    <span>Aerial Image</span>
                                    <i class="fa fa-exclamation-triangle label-orange"
                                       aria-hidden="true"
                                       ng-if="!vm.cachedHasAerialMapImageData">
                                        <md-tooltip md-direction="top">
                                            Aerial image has not been captured.
                                        </md-tooltip>
                                    </i>
                                </md-tab-label>

                                <md-tab-body>
                                    <fieldset id="map-display-fc"
                                              redi-enable-roles="assessment_page_(tabs/sub-tabs)__aerialimage__edit"
                                              ng-if="vm.assessment.assessmentProjectDetail != null">

                                        <div id="map-image-div" ng-if="vm.cachedHasAerialMapImageData">
                                            <md-card style="text-align: center; padding: 20px 20px;">
                                                <div>

                                                    <!-- Switch between showing the raw image or the URL. Raw image takes precedence. -->
                                                    <div ng-switch on="vm.aerialMap.imageBuffer == null"
                                                         style="position: relative; max-width:100%; display: inline-block;">

                                                        <img ng-switch-when="false"
                                                             src="{{vm.aerialMap.imageBuffer}}"
                                                             style="max-width: 100%; object-fit: cover; object-position: 50% 50%;" />

                                                        <img ng-switch-default
                                                             src="{{vm.assessment.assessmentProjectDetail.mapImageFile.url}}"
                                                             style="max-width: 100%; object-fit: cover; object-position: 50% 50%;" />

                                                        <img src="/content/images/north-arrow.svg"
                                                             style="position: absolute; right: 20px; bottom: 20px;
                                                        height: 50px;" />
                                                    </div>

                                                </div>
                                                <div>

                                                    <div>
                                                        <md-checkbox ng-model="vm.assessment.includeMapInExportedPDF"
                                                                     name="includeMapInExportedPDF"
                                                                     ng-disabled="vm.isLocked"
                                                                     style="margin-top: 15px;">
                                                            Include In PDF
                                                        </md-checkbox>
                                                    </div>

                                                    <div>
                                                        <md-button ng-click="vm.saveCurrentAddressAndMapState(); vm.releaseMapImage(vm.aerialMap, 'mapImageFile', 19)"
                                                                   class="md-raised md-warn"
                                                                   ng-disabled="vm.isLocked">
                                                            Discard Image
                                                        </md-button>
                                                    </div>

                                                </div>
                                            </md-card>
                                        </div>

                                        <div id="map-div" ng-show="!vm.cachedHasAerialMapImageData">
                                            <md-card style="text-align: center; padding: 20px 20px;">
                                                <md-card-content>
                                                    <h2>
                                                        <span>{{vm.assessment.assessmentProjectDetail.originalDisplayAddress}}&nbsp;</span>
                                                        <md-button ng-click="vm.resetMapToDefault(vm.aerialMap);"
                                                                   ng-disabled="vm.isLocked"
                                                                   class="md-raised md-warn">
                                                            Recentre
                                                        </md-button>
                                                        <md-button ng-click="vm.captureCurrentView(vm.aerialMap)"
                                                                   ng-disabled="vm.isLocked"
                                                                   class="md-raised md-warn">
                                                            Capture
                                                        </md-button>
                                                        <md-button ng-click="vm.resetToPrePanState();"
                                                                   ng-if="vm.assessmentStateBackup != null"
                                                                   ng-disabled="vm.isLocked"
                                                                   class="md-raised">
                                                            Cancel
                                                        </md-button>
                                                    </h2>
                                                    <div style="position: relative">
                                                        <div id="map_element" style="width: 100%; height: 1200px;">
                                                            <img src="/content/images/north-arrow.svg"
                                                                 style="position: absolute; right: 20px; bottom: 20px;
                                                                        height: 50px; z-index: 9999;" />

                                                            <!-- Loading Icon Overlay -->
                                                            <div ng-if="vm.aerialMap.isBusy"
                                                                 data-cc-spinner="vm.spinnerOptions"
                                                                 style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;
                                                                 background-color: rgba(255, 255, 255, 0.8); z-index: 999;
                                                                 display: flex; flex-direction: column; justify-content: center; align-items: center;" />

                                                        </div>

                                                    </div>
                                                </md-card-content>
                                            </md-card>
                                        </div>

                                    </fieldset>

                                </md-tab-body>
                            </md-tab>

                            <!-- Climate Tab -->
                            <md-tab layout="column"
                                    ng-if="vm.permission_tab_climate_view"
                                    class="graph-card"
                                    style="overflow: hidden;">
                                <md-tab-label>

                                    Climate&nbsp;
                                    <i class="fa fa-exclamation-triangle label-orange" aria-hidden="true"
                                       ng-if="assessmentform.buildingSiteForm.climateForm.$invalid">
                                        <md-tooltip md-direction="top">
                                            {{vm.userFriendlyErrorMessage(assessmentform.buildingSiteForm.climateForm.$error)}}
                                        </md-tooltip>
                                    </i>

                                </md-tab-label>

                                <md-tab-body class="graph-card" style="overflow: hidden;">
                                    <fieldset id="climate-display-fc"
                                              redi-enable-roles="assessment_page_(tabs/sub-tabs)__climate__edit"
                                              style="overflow: hidden;"
                                              ng-if="vm.assessment.assessmentProjectDetail != null">

                                        <climate-overview assessment="vm.assessment"
                                                          disabled="vm.isLocked || !vm.permission_tab_climate_edit"
                                                          nat-hers-changed="vm.adjustNcc2022NatHers()">
                                        </climate-overview>

                                    </fieldset>

                                </md-tab-body>
                            </md-tab>

                            <!-- THR-249: Removed Features Tab -->

                            <!-- Lot Tab -->
                            <md-tab ng-if="vm.permission_tab_lot_view">
                                <md-tab-label>
                                    Lot
                                </md-tab-label>
                                <md-tab-body>

                                    <!-- Lot Information Card -->
                                    <md-card>
                                        <md-card-header>
                                            <span class="md-headline">
                                                Lot Information
                                            </span>
                                        </md-card-header>

                                        <md-card-content redi-enable-roles="assessment_page_(tabs/sub-tabs)__lot__edit">

                                            <!-- Number of Boundaries / Boundary Sides -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Number of Boundaries</label>
                                                <input name="numberOfBoundaries"
                                                       ng-disabled="!vm.permission_tab_assessment_edit"
                                                       formatted-number
                                                       decimals="0"
                                                       ng-model="vm.assessment.assessmentProjectDetail.boundarySides"/>
                                            </md-input-container>

                                            <!-- ******** Parcel Area (m2) ******** -->
                                            <md-input-container flex="100" class="md-block">
                                                <label>Parcel Area m<sup>2</sup></label>
                                                <input type="text" name="PlanType"
                                                       ng-disabled="!vm.permission_tab_assessment_edit"
                                                       ng-model="vm.assessment.assessmentProjectDetail.parcelArea" />
                                            </md-input-container>

                                            <!-- Lot Width (m) -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Lot Width (m)</label>
                                                <input name="lotWidth"
                                                       class="lightweight"
                                                       formatted-number
                                                       decimals="8"
                                                       ng-disabled="!vm.permission_tab_assessment_edit"
                                                       ng-model="vm.assessment.assessmentProjectDetail.lotWidth"
                                                       ng-required="false"
                                                       ng-change="vm.narrowLotAbortOverride()" />
                                            </md-input-container>

                                            <!-- Lot Length (m) -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Lot Length (m)</label>
                                                <input name="lotLength"
                                                       class="lightweight"
                                                       formatted-number
                                                       decimals="8"
                                                       ng-disabled="!vm.permission_tab_assessment_edit"
                                                       ng-model="vm.assessment.assessmentProjectDetail.lotLength"
                                                       ng-required="false"/>
                                            </md-input-container>

                                            <!-- Lot Description -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Lot Description</label>
                                                <md-select name="lotDescription"
                                                           ng-disabled="!vm.permission_tab_assessment_edit"
                                                           ng-model="vm.assessment.assessmentProjectDetail.lotDescription"
                                                           md-container-class="lotDescriptionHeightOverride">
                                                    <md-option ng-value="null"></md-option>
                                                    <md-option value="Battle-Axe (Rear Strata)">Battle-Axe (Rear Strata)</md-option>
                                                    <md-option value="Corner (Truncation)">Corner (Truncation)</md-option>
                                                    <md-option value="Front Strata">Front Strata</md-option>
                                                    <md-option value="Irregular">Irregular</md-option>
                                                    <md-option value="Parallel Road">Parallel Road</md-option>
                                                    <md-option value="Standard">Standard</md-option>
                                                    <md-option value="Other">Other</md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- Corner Lot -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Corner Lot</label>
                                                <md-select name="cornerLot"
                                                           ng-disabled="!vm.permission_tab_assessment_edit"
                                                           ng-model="vm.assessment.assessmentProjectDetail.cornerBlock">
                                                    <md-option ng-value="null"></md-option>
                                                    <md-option value="No">No</md-option>
                                                    <md-option value="Left-hand Corner">Left-hand Corner</md-option>
                                                    <md-option value="Right-hand Corner">Right-hand Corner</md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- Narrow Lot (needs to be brrrr'd to recalc based on lot width but user can override it...) -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Narrow Lot</label>
                                                <md-select name="narrowLot"
                                                           ng-disabled="!vm.permission_tab_assessment_edit"
                                                           ng-model="vm.assessment.assessmentProjectDetail.narrowLot"
                                                           ng-change="vm.narrowLotManualOverride()">
                                                    <md-option ng-value="null"></md-option>
                                                    <md-option ng-value="true">Yes</md-option>
                                                    <md-option ng-value="false">No</md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- Rear Loaded -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Rear Loaded</label>
                                                <md-select name="rearLaneway"
                                                           ng-disabled="!vm.permission_tab_assessment_edit"
                                                           ng-model="vm.assessment.assessmentProjectDetail.rearLaneway">
                                                    <md-option ng-value="null"></md-option>
                                                    <md-option ng-value="true">Yes</md-option>
                                                    <md-option ng-value="false">No</md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- Rural Lot (needs to be brrrr'd to recalc based on Parcel Area) -->
                                            <md-input-container class="md-block" flex="100">
                                                <label>Rural Lot</label>
                                                <md-select name="ruralLot"
                                                           ng-disabled="!vm.permission_tab_assessment_edit"
                                                           ng-model="vm.assessment.assessmentProjectDetail.ruralLot">
                                                    <md-option ng-value="null"></md-option>
                                                    <md-option ng-value="true">Yes</md-option>
                                                    <md-option ng-value="false">No</md-option>
                                                </md-select>
                                            </md-input-container>

                                        </md-card-content>
                                    </md-card>

                                </md-tab-body>
                            </md-tab>

                        </md-tabs>
                    </md-tab-body>

                </md-tab>

                <!-- General -->
                <md-tab ng-if="vm.permission_tab_general_view"
                        layout="column">
                    <md-tab-label>
                        <span>
                            General&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="vm.cachedBuildingGeneralTabHasErrors">
                                <md-tooltip md-direction="top">
                                    {{vm.cachedBuildingGeneralTabHasErrors}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>

                    <md-tab-body>

                        <fieldset id="building-general-tab-fieldset"
                                  ng-disabled="!vm.permission_tab_general_edit"
                                  ng-form="buildingGeneralForm">
                            <md-tabs md-selected="vm.selectedOptionIndex">

                                <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                        ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;"
                                        layout="column">

                                    <md-tab-label>
                                        <span>
                                            {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                            <i class="fa fa-exclamation-triangle label-orange"
                                               aria-hidden="true"
                                               ng-if="(vm.cachedBuildingGeneralOptionErrors[option.optionIndex]['proposed'] ||
                                                (vm.cachedBuildingGeneralOptionErrors[option.optionIndex]['reference'] && (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')))">
                                            </i>
                                        </span>
                                    </md-tab-label>

                                    <md-tab-body>

                                        <!-- Radio Selector to switch between proposed and reference building zones -->
                                        <div flex="100"
                                             style="margin-top: 20px; margin-bottom: 20px;"
                                             layout="row"
                                             layout-align="left center">

                                            <md-radio-group ng-model="vm.buildingToShow"
                                                            layout="row"
                                                            style="margin-left: 30px;"
                                                            ng-disabled="vm.isLocked">

                                                <md-radio-button ng-value="'proposed'">
                                                    Proposed Building&nbsp;
                                                    <i class="fa fa-exclamation-triangle label-orange"
                                                       aria-hidden="true"
                                                       ng-if="vm.cachedBuildingGeneralOptionErrors[option.optionIndex]['proposed']">
                                                    </i>
                                                </md-radio-button>
                                                <md-radio-button ng-value="'reference'"
                                                                 ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                    {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                                    <i class="fa fa-exclamation-triangle label-orange"
                                                       aria-hidden="true"
                                                       ng-if="vm.cachedBuildingGeneralOptionErrors[option.optionIndex]['reference']">
                                                    </i>
                                                </md-radio-button>
                                            </md-radio-group>
                                        </div>

                                        <!-- Copy to from -->
                                        <md-card>
                                            <md-card-content>
                                                <!-- Template Selection + Copy to -> from buttons -->
                                                <div class="layout-column">
                                                    <!-- Copy From -> To Buttons -->
                                                    <div layout="row"
                                                         class="md-block"
                                                         style="margin-left: auto">
                                                        <md-button ng-click="vm.copyGeneralToFrom(option, option[vm.buildingToShow ], vm.assessment.allComplianceOptions[0][vm.buildingToShow ], vm.assessment.allComplianceOptions[0])"
                                                                   class="md-raised"
                                                                   ng-show="!vm.isLocked && vm.showCopyBaselineForOption(vm.assessment.allComplianceOptions[0]) && option != vm.assessment.allComplianceOptions[0]">
                                                            Copy Baseline
                                                        </md-button>

                                                        <md-button ng-click="vm.copyGeneralToFrom(option, option.reference, option.proposed)"
                                                                   class="md-raised"
                                                                   ng-show="!vm.isLocked && vm.buildingToShow =='reference'">
                                                            Copy Proposed
                                                        </md-button>

                                                        <!-- Now for every compliance option EXCEPT THIS ONE AND THE BASELINE show a "copy Option X" button -->
                                                        <md-button ng-repeat="opt in vm.optionsNotThisOrBaseline(option)"
                                                                   ng-if="!vm.isLocked && option.optionIndex != 0 && vm.showCopyBaselineForOption(opt)"
                                                                   ng-click="vm.copyGeneralToFrom(option, option[vm.buildingToShow ], opt[vm.buildingToShow ], opt)">
                                                            Copy Option {{opt.optionIndex}}
                                                        </md-button>
                                                    </div>
                                                </div>
                                            </md-card-content>
                                        </md-card>

                                        <!-- General -->
                                        <building-general-design ng-show="vm.buildingToShow == 'proposed'"
                                                                 source="option.proposed"
                                                                 compliance-option="option"
                                                                 source-type="vm.buildingToShow"
                                                                 disabled="vm.isLocked || !vm.permission_tab_general_edit"></building-general-design>

                                        <building-general-design ng-show="vm.buildingToShow == 'reference'"
                                                                 source="option.reference"
                                                                 compliance-option="option"
                                                                 source-type="vm.buildingToShow"
                                                                 disabled="vm.isLocked || !vm.permission_tab_general_edit"></building-general-design>
                                        <!-- Spaces -->
                                        <floor-plan-data ng-if="option[vm.buildingToShow] != null && vm.buildingToShow == 'proposed'"
                                                         source="option.proposed"
                                                         compliance-option="option"
                                                         source-type="vm.buildingToShow"
                                                         show-location="true"
                                                         disabled="!vm.permission_tab_general_edit"
                                                         is-template="false">
                                        </floor-plan-data>

                                        <floor-plan-data ng-if="option[vm.buildingToShow] != null && vm.buildingToShow == 'reference'"
                                                         source="option.reference"
                                                         compliance-option="option"
                                                         source-type="vm.buildingToShow"
                                                         show-location="true"
                                                         disabled="!vm.permission_tab_general_edit"
                                                         is-template="false">
                                        </floor-plan-data>

                                        <!-- Roofs -->
                                        <building-general-roofs ng-if="option[vm.buildingToShow] != null && vm.buildingToShow == 'proposed'"
                                                                source="option.proposed"
                                                                compliance-option="option"
                                                                source-type="vm.buildingToShow"
                                                                show-location="true"
                                                                disabled="!vm.permission_tab_general_edit">
                                        </building-general-roofs>

                                        <building-general-roofs ng-if="option[vm.buildingToShow] != null && vm.buildingToShow == 'reference'"
                                                                source="option.reference"
                                                                compliance-option="option"
                                                                source-type="vm.buildingToShow"
                                                                show-location="true"
                                                                disabled="!vm.permission_tab_general_edit">
                                        </building-general-roofs>

                                    </md-tab-body>
                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- Design -->
                <md-tab label="Design"
                        ng-if="vm.permission_tab_design_view"
                        layout="column">
                    <md-tab-label>
                        <span>
                            Design&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="vm.buildingZonesHasErrors()">
                                <md-tooltip md-direction="top">
                                    {{vm.buildingZonesHasErrors()}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>

                        <md-tabs ng-form="buildingZonesForm"
                                 md-selected="vm.selectedOptionIndex">
                            <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                    layout="column"
                                    ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex">

                                <md-tab-label>
                                    <span>
                                        {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                        <i class="fa fa-exclamation-triangle label-orange"
                                           aria-hidden="true"
                                           ng-if="option.proposed.zonesFormErrors === true || option.reference.zonesFormErrors === true">
                                        </i>
                                    </span>
                                </md-tab-label>

                                <md-tab-body>

                                    <!-- Radio Selector to switch between proposed and reference building zones -->
                                    <div flex="100"
                                         style="margin-top: 20px; margin-bottom: 20px;"
                                         layout="row"
                                         layout-align="left center">

                                        <md-radio-group ng-model="vm.buildingToShow"
                                                        layout="row"
                                                        style="margin-left: 30px;">

                                            <md-radio-button ng-value="'proposed'">
                                                Proposed Building&nbsp;
                                                <i class="fa fa-exclamation-triangle label-orange"
                                                   aria-hidden="true"
                                                   ng-if="vm.buildingZonesOptionHasErrors(option, option.proposed, 'proposed')">
                                                </i>
                                            </md-radio-button>
                                            <md-radio-button ng-value="'reference'"
                                                             ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                                <i class="fa fa-exclamation-triangle label-orange"
                                                   aria-hidden="true"
                                                   ng-if="vm.buildingZonesOptionHasErrors(option, option.reference, 'reference')">
                                                </i>
                                            </md-radio-button>
                                        </md-radio-group>
                                    </div>

                                    <!-- Building Floor Zones -->
                                    <zone-list ng-show="vm.buildingToShow == 'proposed'"
                                               source="option.proposed"
                                               source-type="'proposed'"
                                               compliance-option="option"
                                               enable-template-selection="true"
                                               disabled="vm.isLocked || !vm.permission_tab_design_edit"
                                               prevent-update-on-load="true"
                                               is-template="false"
                                               baseline-option="vm.assessment.allComplianceOptions[0]"
                                               compliance-options="vm.assessment.allComplianceOptions">
                                    </zone-list>

                                    <zone-list ng-if="option.complianceMethod.complianceMethodCode === 'CMPerfSolution' ||
                                                      option.complianceMethod.complianceMethodCode === 'CMPerfSolutionDTS'"
                                               ng-show="vm.buildingToShow == 'reference'"
                                               source="option.reference"
                                               source-type="'reference'"
                                               compliance-option="option"
                                               enable-template-selection="true"
                                               disabled="vm.isLocked || !vm.permission_tab_design_edit"
                                               prevent-update-on-load="true"
                                               is-template="false"
                                               baseline-option="vm.assessment.allComplianceOptions[0]"
                                               compliance-options="vm.assessment.allComplianceOptions">
                                    </zone-list>

                                    <!-- <floor-plan-data> has moved to General tab... -->

                                    <!-- <zone-summary> has moved to the Analytics tab -->

                                </md-tab-body>
                            </md-tab>
                        </md-tabs>
                    </md-tab-body>
                </md-tab>

                <!-- Construction -->
                <md-tab ng-if="vm.permission_tab_construction_view"
                        layout="column"
                        ng-click="vm.navigateToTab('construction')"
                    <md-tab-label>
                        <span>
                            Construction&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="(vm.buildingElementsFormHasErrors('construction') || vm.buildingElementsHasErrors() || buildingElementsForm.$invalid)">
                                <md-tooltip md-direction="top">
                                    {{vm.userFriendlyErrorMessage(buildingElementsForm.$error) + vm.buildingElementsHasErrors()}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>

                        <fieldset id="building-elements-tab-fieldset"
                                  ng-disabled="vm.isLocked || !vm.permission_tab_construction_edit"
                                  ng-form="buildingElementsForm">
                            <md-tabs md-selected="vm.selectedOptionIndex">

                                <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                        ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;"
                                        layout="column">

                                    <md-tab-label>
                                        <span>
                                            {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                            <i class="fa fa-exclamation-triangle label-orange"
                                               aria-hidden="true"
                                               ng-if="(vm.buildingElementsOptionHasErrors(option, 'proposed', 'construction') ||
                                                (vm.buildingElementsOptionHasErrors(option, 'reference', 'construction') &&
                                                    (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')))">
                                            </i>
                                        </span>
                                    </md-tab-label>

                                    <md-tab-body>

                                        <!-- Radio Selector to switch between proposed and reference buildings -->
                                        <div flex="100"
                                             style="margin-top: 20px; margin-bottom: 20px;"
                                             layout="row"
                                             layout-align="left center">

                                            <md-radio-group ng-model="vm.buildingToShow"
                                                            layout="row"
                                                            style="margin-left: 30px;">

                                                <md-radio-button ng-value="'proposed'">
                                                    Proposed Building&nbsp;
                                                    <i class="fa fa-exclamation-triangle label-orange"
                                                       aria-hidden="true"
                                                       ng-if="vm.buildingElementsOptionHasErrors(option, 'proposed', 'construction')">
                                                    </i>
                                                </md-radio-button>
                                                <md-radio-button ng-value="'reference'"
                                                                 ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                    {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                                    <i class="fa fa-exclamation-triangle label-orange"
                                                       aria-hidden="true"
                                                       ng-if="vm.buildingElementsOptionHasErrors(option, 'reference', 'construction') &&
                                                        (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')">
                                                    </i>
                                                </md-radio-button>
                                            </md-radio-group>

                                            <md-checkbox ng-disabled="vm.isLocked || !vm.permission_tab_construction_edit"
                                                         ng-if="(option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS')"
                                                         ng-model="option.reference.includeBuildingElementsInReport"
                                                         style="margin: 0px; margin-bottom: 0px; margin-left: auto; transform: scale(0.9);"
                                                         class="vertically-condensed vertically-condensed-ex checkbox-aligner">
                                                Include {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}} in Report
                                            </md-checkbox>
                                        </div>

                                        <!-- Actual list of all our construction data -->
                                        <!-- have to pass in sectorDetermination :( -->
                                        <building-construction-data ng-show="vm.buildingToShow == 'proposed'"
                                                                    construction-category-list="vm.constructionTabCategories"
                                                                    general-section-display="'construction'"
                                                                    show-services="true"
                                                                    building="option.proposed"
                                                                    building-type="'proposed'"
                                                                    option="option"
                                                                    is-template="false"
                                                                    baseline-option="vm.assessment.allComplianceOptions[0]"
                                                                    disabled="vm.isLocked || !vm.permission_tab_construction_edit"
                                                                    client-id="vm.assessment.job.clientId"
                                                                    comparison-building="!option.isBaselineSimulation ? vm.assessment.allComplianceOptions[0].proposed : null"
                                                                    compliance-options="vm.assessment.allComplianceOptions"
                                                                    sector-determination="option.sectorDetermination">
                                        </building-construction-data>
                                        <building-construction-data ng-if="(option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')"
                                                                    ng-show="vm.buildingToShow == 'reference'"
                                                                    construction-category-list="vm.constructionTabCategories"
                                                                    general-section-display="'construction'"
                                                                    show-services="true"
                                                                    building="option.reference"
                                                                    building-type="'reference'"
                                                                    option="option"
                                                                    is-template="false"
                                                                    baseline-option="vm.assessment.allComplianceOptions[0]"
                                                                    comparison-building="!option.isBaselineSimulation ? vm.assessment.allComplianceOptions[0].reference : null"
                                                                    disabled="vm.isLocked || !vm.permission_tab_construction_edit"
                                                                    client-id="vm.assessment.job.clientId"
                                                                    compliance-options="vm.assessment.allComplianceOptions"
                                                                    sector-determination="option.sectorDetermination">
                                        </building-construction-data>
                                    </md-tab-body>
                                </md-tab>
                            </md-tabs>

                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- Openings -->
                <md-tab ng-if="vm.permission_tab_openings_view"
                        layout="column"
                        ng-click="vm.navigateToTab('openings')"
                    <md-tab-label>
                        <span>
                            Openings&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="(vm.buildingElementsFormHasErrors('opening') || openingsForm.$invalid)">
                                <md-tooltip md-direction="top">
                                    {{vm.userFriendlyErrorMessage(openingsForm.$error) + vm.buildingElementsHasErrors()}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>

                        <fieldset id="building-openings-tab-fieldset"
                                  ng-disabled="vm.isLocked || !vm.permission_tab_openings_edit"
                                  ng-form="openingsForm">
                            <md-tabs md-selected="vm.selectedOptionIndex">

                                <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                        ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;"
                                        layout="column">

                                    <md-tab-label>
                                        <span>
                                            {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                            <i class="fa fa-exclamation-triangle label-orange"
                                               aria-hidden="true"
                                               ng-if="(vm.buildingElementsOptionHasErrors(option, 'proposed', 'opening') ||
                                                (vm.buildingElementsOptionHasErrors(option, 'reference', 'opening') &&
                                                    (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')))">
                                            </i>
                                        </span>
                                    </md-tab-label>

                                    <md-tab-body>

                                        <!-- Radio Selector to switch between proposed and reference buildings -->
                                        <div flex="100"
                                             style="margin-top: 20px; margin-bottom: 20px;"
                                             layout="row"
                                             layout-align="left center">

                                                <md-radio-group ng-model="vm.buildingToShow"
                                                                layout="row"
                                                                style="margin-left: 30px;">

                                                    <md-radio-button ng-value="'proposed'">
                                                        Proposed Building&nbsp;
                                                        <i class="fa fa-exclamation-triangle label-orange"
                                                           aria-hidden="true"
                                                           ng-if="vm.buildingElementsOptionHasErrors(option, 'proposed', 'opening')">
                                                        </i>
                                                    </md-radio-button>
                                                    <md-radio-button ng-value="'reference'"
                                                                     ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                        {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                                        <i class="fa fa-exclamation-triangle label-orange"
                                                           aria-hidden="true"
                                                           ng-if="vm.buildingElementsOptionHasErrors(option, 'reference', 'opening') &&
                                                            (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')">
                                                        </i>
                                                    </md-radio-button>
                                                </md-radio-group>

                                                <md-checkbox ng-disabled="vm.isLocked || !vm.permission_tab_openings_edit"
                                                             ng-if="(option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS')"
                                                             ng-model="option.reference.includeBuildingElementsInReport"
                                                             style="margin: 0px; margin-bottom: 0px; margin-left: auto; transform: scale(0.9);"
                                                             class="vertically-condensed vertically-condensed-ex checkbox-aligner">
                                                    Include {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}} in Report
                                                </md-checkbox>

                                        </div>

                                        <!-- Actual list of all our construction data -->
                                        <building-construction-data ng-show="vm.buildingToShow == 'proposed'"
                                                                    construction-category-list="vm.openingTabCategories"
                                                                    general-section-display="'opening'"
                                                                    building="option.proposed"
                                                                    building-type="'proposed'"
                                                                    option="option"
                                                                    is-template="false"
                                                                    baseline-option="vm.assessment.allComplianceOptions[0]"
                                                                    disabled="vm.isLocked || !vm.permission_tab_openings_edit"
                                                                    client-id="vm.assessment.job.clientId"
                                                                    comparison-building="!option.isBaselineSimulation ? vm.assessment.allComplianceOptions[0].proposed : null"
                                                                    compliance-options="vm.assessment.allComplianceOptions"
                                                                    launch-glazing-calc-callback="vm.launchGlazingCalcExportModal(vm.assessment, option, vm.buildingToShow)"
                                                                    sector-determination="option.sectorDetermination">
                                        </building-construction-data>
                                        <building-construction-data ng-if="(option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')"
                                                                    ng-show="vm.buildingToShow == 'reference'"
                                                                    construction-category-list="vm.openingTabCategories"
                                                                    general-section-display="'opening'"
                                                                    building="option.reference"
                                                                    building-type="'reference'"
                                                                    option="option"
                                                                    is-template="false"
                                                                    baseline-option="vm.assessment.allComplianceOptions[0]"
                                                                    comparison-building="!option.isBaselineSimulation ? vm.assessment.allComplianceOptions[0].reference : null"
                                                                    disabled="vm.isLocked || !vm.permission_tab_openings_edit"
                                                                    client-id="vm.assessment.job.clientId"
                                                                    compliance-options="vm.assessment.allComplianceOptions"
                                                                    launch-glazing-calc-callback="vm.launchGlazingCalcExportModal(vm.assessment, option, vm.buildingToShow)"
                                                                    sector-determination="option.sectorDetermination">
                                        </building-construction-data>

                                    </md-tab-body>
                                </md-tab>
                            </md-tabs>

                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- Services -->
                <md-tab ng-if="vm.permission_tab_services_view"
                        layout="column"
                        ng-click="vm.navigateToTab('services')"
                    <md-tab-label>
                        <span>
                            Services
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="(vm.buildingServicesFormHasErrors())">
                                <md-tooltip md-direction="top">
                                    {{vm.userFriendlyErrorMessage(servicesForm.$error) + vm.buildingElementsHasErrors()}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>

                        <fieldset id="services-tab-fieldset"
                                  ng-form="servicesForm">
                            <md-tabs md-selected="vm.selectedOptionIndex">

                                <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                        ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;"
                                        layout="column">

                                    <md-tab-label>
                                        <span>
                                            {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                            <i class="fa fa-exclamation-triangle label-orange"
                                               aria-hidden="true"
                                               ng-if="option.proposed.servicesFormErrors === true ||
                                                      option.reference.servicesFormErrors === true">
                                            </i>
                                        </span>
                                    </md-tab-label>

                                    <md-tab-body>

                                        <!-- Radio Selector to switch between proposed and reference buildings -->
                                        <div flex="100"
                                             style="margin-top:10px; display:flex; justify-content:space-between;"
                                             layout="row"
                                             layout-align="left center">

                                            <md-radio-group ng-model="vm.buildingToShow"
                                                            layout="row"
                                                            style="margin-left: 30px;">

                                                <md-radio-button ng-value="'proposed'">
                                                    Proposed Building&nbsp;
                                                    <i class="fa fa-exclamation-triangle label-orange"
                                                       aria-hidden="true"
                                                       ng-if="vm.buildingServicesOptionHasErrors(option, option.proposed)">
                                                    </i>
                                                </md-radio-button>
                                                <md-radio-button ng-value="'reference'"
                                                                 ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                    {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                                    <i class="fa fa-exclamation-triangle label-orange"
                                                       aria-hidden="true"
                                                       ng-if="vm.buildingServicesOptionHasErrors(option, option.reference, true)">
                                                    </i>
                                                </md-radio-button>
                                            </md-radio-group>

                                            <div style="float:right; display:flex; height:48px;">
                                                <md-button ng-if="vm.permission_action_exportwoh" ng-disabled="false" class="md-primary md-raised"
                                                           ng-click="vm.exportWoh(option);"
                                                           style="margin-left: auto; margin-right: 20px;float: right;">
                                                    Export Whole-Of-Home
                                                </md-button>
                                                <md-checkbox ng-disabled="vm.isLocked || !vm.permission_tab_construction_edit|| !vm.permission_tab_services_edit"
                                                             ng-if="(option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS')"
                                                             ng-model="option.reference.includeBuildingElementsInReport"
                                                             style="margin: 0px; margin-bottom: 0px; margin-left: auto; transform: scale(0.9);"
                                                             class="vertically-condensed vertically-condensed-ex checkbox-aligner">
                                                    Include {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}} in Report
                                                </md-checkbox>
                                            </div>

                                        </div>

                                        <building-services-data ng-show="vm.buildingToShow == 'proposed'"
                                                                option="option"
                                                                building="option.proposed"
                                                                building-type="'proposed'"
                                                                comparison-building="!option.isBaselineSimulation ? vm.assessment.allComplianceOptions[0].proposed : null"
                                                                service-category-list="vm.serviceCategoryList"
                                                                disabled="vm.isLocked || !vm.permission_tab_services_edit"
                                                                compliance-options="vm.assessment.allComplianceOptions"
                                                                baseline-option="vm.assessment.allComplianceOptions[0]">
                                        </building-services-data>

                                        <building-services-data ng-show="vm.buildingToShow == 'reference'"
                                                                option="option"
                                                                building="option.reference"
                                                                building-type="'reference'"
                                                                service-category-list="vm.serviceCategoryList"
                                                                comparison-building="!option.isBaselineSimulation ? vm.assessment.allComplianceOptions[0].reference : null"
                                                                disabled="vm.isLocked || !vm.permission_tab_services_edit"
                                                                compliance-options="vm.assessment.allComplianceOptions"
                                                                baseline-option="vm.assessment.allComplianceOptions[0]">
                                        </building-services-data>

                                    </md-tab-body>
                                </md-tab>
                            </md-tabs>

                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- Drawings -->
                <md-tab label="Drawings"
                        ng-if="vm.permission_tab_drawings_view"
                        layout="column"
                        ng-click="vm.navigateToTab('drawings')">
                    <md-tab-label>
                        <span>
                            Drawings&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="WorkingDrawingsForm.$invalid || vm.workingDrawingsHasErrors()">
                                <md-tooltip md-direction="top">
                                    {{vm.userFriendlyErrorMessage(WorkingDrawingsForm.$error) + " " + vm.workingDrawingsHasErrors()}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>
                        <div id="drawings-tab-fieldset"
                             ng-disabled="(vm.isLocked || vm.permission_tab_drawings_edit == false)"
                             ng-form="WorkingDrawingsForm">

                            <md-tabs md-selected="vm.selectedOptionIndex">
                                <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                        layout="column"
                                        ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;">

                                    <md-tab-label>
                                        <span>
                                            {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                            <i class="fa fa-exclamation-triangle label-orange"
                                               aria-hidden="true"
                                               ng-if="vm.workingDrawingsOptionHasErrors(option) !== false">
                                            </i>
                                        </span>
                                    </md-tab-label>

                                    <md-tab-body>
                                        <building-drawings assessment="vm.assessment"
                                                           option="option"
                                                           disabled="(vm.isLocked || vm.permission_tab_drawings_edit == false) &&
                                                                      vm.assessment.statusCode !== 'ACompliance'"
                                                           job-files="vm.jobFiles"
                                                           client-options="vm.assessment.job.client.clientOptions">
                                        </building-drawings>
                                    </md-tab-body>

                                </md-tab>
                            </md-tabs>
                        </div>

                    </md-tab-body>
                </md-tab>

                <!-- Simulation -->
                <md-tab label="Simulation"
                        ng-if="vm.permission_tab_simulation_view"
                        layout="column"
                        ng-click="vm.navigateToTab('simulation')">
                    <md-tab-label>
                        <span>
                            Simulation&nbsp;
                            <i class="fa fa-exclamation-triangle label-orange"
                               aria-hidden="true"
                               ng-if="(assessmentform.assessmentOutcomesRootForm.$invalid ||
                                        vm.assessmentOutcomesHasErrors()) && !vm.getSelectedComplianceOption()">
                                <md-tooltip md-direction="top">
                                    {{vm.userFriendlyErrorMessage(assessmentform.assessmentOutcomesRootForm.$error) + " " + vm.assessmentOutcomesHasErrors()}}
                                </md-tooltip>
                            </i>
                        </span>
                    </md-tab-label>
                    <md-tab-body>
                        <fieldset ng-if="vm.assessment.assessmentProjectDetail != null"
                                  redi-enable-roles="assessment_page_(tabs/sub-tabs)__simulation__edit"
                                  ng-form="assessmentOutcomesRootForm">

                            <assessment-outcomes is-locked="vm.isLocked || !vm.permission_tab_simulation_edit"
                                                 allow-option-select="vm.allowOptionSelect()"
                                                 assessment="vm.assessment"
                                                 client-id="vm.assessment.job.clientId"
                                                 compliance-status-list="vm.complianceStatusList"
                                                 compliance-method-list="vm.complianceMethodList"
                                                 compliance-method-code="vm.assessment.allComplianceOptions[0].complianceMethodCode"
                                                 assessment-software-list="vm.allAssessmentSoftwareList"
                                                 set-final-compliance-method="vm.setFinalComplianceMethod({saveAssessment})"
                                                 job-files="vm.jobFiles"></assessment-outcomes>

                            <!-- Assessor notes -->
                            <md-card>

                                <md-card-header style="display: block; margin-bottom: 8px; padding-bottom: 0px; display: grid; grid-template-columns: auto 1fr; align-content: center;">
                                    <div class="md-title">
                                        Assessment Notes
                                    </div>
                                    <md-checkbox style="justify-self: end;"
                                                 ng-model="vm.assessment.assessorNotesNotApplicable"
                                                 ng-disabled="vm.isLocked || !vm.permission_tab_simulation_edit">
                                        Not Applicable
                                    </md-checkbox>
                                </md-card-header>

                                <md-card-body ng-if="!vm.assessment.assessorNotesNotApplicable"
                                              style="padding: 10px;">
                                    <textarea class="assessment-notes-input"
                                              ng-model="vm.assessment.assessorNotes"
                                              ng-disabled="vm.isLocked || !vm.permission_tab_simulation_edit">

                                    </textarea>
                                </md-card-body>

                            </md-card>

                        </fieldset>
                    </md-tab-body>
                </md-tab>

                <!-- MULTI-SIM -->
                <md-tab label="MULTI-SIM"
                        ng-if="vm.permission_tab_multisim_view"
                        layout="column"
                        ng-click="vm.navigateToTab('multisim')">
                    <md-tabs md-selected="vm.selectedOptionIndex">
                        <!-- FOR EACH option -->
                        <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;"
                                layout="column">

                            <md-tab-label>
                                <span>
                                    {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                    <i class="fa fa-exclamation-triangle label-orange"
                                       aria-hidden="true"
                                       ng-if="(vm.buildingElementsOptionHasErrors(option, 'proposed', 'construction') ||
                                        (vm.buildingElementsOptionHasErrors(option, 'reference', 'construction') &&
                                            (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode=='CMPerfSolutionDTS')))">
                                    </i>
                                </span>
                            </md-tab-label>

                            <md-tab-body>
                                <md-card class="ncc2022OuterCard">

                                    <div ng-if="option.ncc2022 == null || option.ncc2022.length == 0" class="ncc2022NoDataSaved">No Multi-Sim data saved.</div>

                                    <!-- FOR EACH Nat Hers Climate Zone -->
                                    <fieldset redi-enable-roles="assessment_page_(tabs/sub-tabs)__multisim__edit" class="ncc2022CardsContainer" ng-repeat="climateZoneTable in vm.getMainNcc2022Tables(option) track by climateZoneTable.id">

                                        <md-card class="ncc2022Card" ng-repeat="thisTable in vm.getOriginalAndFlippedTable(option, climateZoneTable) track by thisTable.id">
                                            <div class="ncc2022FieldContainer">
                                                <!-- Certification -->
                                                <md-input-container flex="100" class="md-block ncc2022Field">
                                                    <label>Certification</label>
                                                    <md-select ng-disabled="vm.permission_tab_multisim_edit == false || thisTable.flippedFromId != null"
                                                               name="certification"
                                                               ng-required="true"
                                                               ng-model="thisTable.certification"
                                                               ng-change="vm.ncc2022FieldChanged(option, thisTable, 'certification')">
                                                        <md-option ng-value="item.title"
                                                                   ng-repeat="item in vm.certificationOptionsForThisTable(option, thisTable) track by item.title">
                                                            {{item.title}}
                                                        </md-option>
                                                    </md-select>
                                                </md-input-container>
                                                <!-- Assessment Method -->
                                                <md-input-container flex="100" class="md-block ncc2022Field">
                                                    <label>Assessment Method</label>
                                                    <md-select ng-disabled="vm.permission_tab_multisim_edit == false || thisTable.flippedFromId != null"
                                                               name="assessmentMethod"
                                                               ng-required="true"
                                                               ng-model="thisTable.assessmentMethod"
                                                               ng-change="vm.ncc2022FieldChanged(option, thisTable, 'assessmentMethod')">
                                                        <md-option ng-value="item.description"
                                                                   ng-repeat="item in vm.assessmentMethodOptionsForThisTable(option, thisTable) track by item.description">
                                                            {{item.description}}
                                                        </md-option>
                                                    </md-select>
                                                </md-input-container>
                                                <!-- NatHERS Climate Zone -->
                                                <md-input-container flex="100" class="md-block ncc2022Field">
                                                    <label>NatHERS Climate Zone</label>
                                                    <md-select ng-disabled="vm.permission_tab_multisim_edit == false || thisTable.flippedFromId != null"
                                                               name="natHersClimateZone"
                                                               ng-required="true"
                                                               ng-model="thisTable.natHersClimateZone"
                                                               ng-change="vm.ncc2022FieldChanged(option, thisTable, 'natHersClimateZone')">
                                                        <md-option ng-value="item.description"
                                                                   ng-repeat="item in vm.natHersOptionsForThisTable(option, thisTable) track by item.description">
                                                            {{item.description}}
                                                        </md-option>
                                                    </md-select>
                                                </md-input-container>
                                                <!-- Floorplan Configuration -->
                                                <md-input-container class="md-block readonly-data" style="margin-top: 13px;">
                                                    <label>Floorplan Configuration</label>
                                                    <span class="read-only-field-value"
                                                          style="min-width: 100px;">
                                                        {{thisTable.flippedFromId == null ? "Original" : "Flip"}}
                                                    </span>
                                                </md-input-container>
                                                <div class="ncc2022-active-remove-container">
                                                    <!-- Active Toggle -->
                                                    <label>Active</label>
                                                    <md-switch class="ncc2022-active-switch" ng-model="thisTable.isActive" ng-change="vm.toggleNcc2022Active(option, thisTable)" ng-disabled="!vm.ncc2022OriginalIsFlipped(option, thisTable)"></md-switch>
                                                    <!-- Flip Toggle -->
                                                    <div ng-if="thisTable.flippedFromId == null">
                                                        <label>Flip</label>
                                                        <md-switch class="ncc2022-flip-switch" ng-model="thisTable.hasFlip" ng-change="vm.toggleNcc2022Flip(option, thisTable)"></md-switch>
                                                    </div>
                                                    <!-- Remove Button -->
                                                    <md-button ng-if="vm.permission_action_deletemultisim && thisTable.flippedFromId == null"
                                                               style="margin-bottom: 10px;"
                                                               class="md-raised md-icon-button md-warn ncc2022-remove-button"
                                                               title="Delete Option"
                                                               ng-click="vm.removeNcc2022(option, thisTable)">
                                                        <i class="fa fa-eraser fa-lg"></i>
                                                    </md-button>
                                                </div>
                                            </div>
                                            <!-- Table -->
                                            <table class="ncc2022Table" id="ncc2022Table-{{option.optionIndex}}-{{thisTable.id}}">
                                                <colgroup>
                                                    <col span="1" style="width: 50px;">
                                                    <col span="1" style="width: 50px;">
                                                    <col span="1" style="width: 50px;">
                                                    <col span="1" style="width: 50px;">
                                                    <col span="1" style="width: 50px;">
                                                </colgroup>
                                                <thead>
                                                    <tr>
                                                        <th><div class="northOffsetHeader">
                                                            North Offset (&deg;)
                                                            <img class="northOffsetSortButton"
                                                                 src="../../../content/images/sort-arrow.png"
                                                                 style="width:22px; height:auto;"
                                                                 ng-class="{
                                                                    'sortByAlphaSelected': thisTable.sortByAlpha,
                                                                    'northOffsetSortButtonFlipped': thisTable.flippedFromId != null
                                                                 }"
                                                                 ng-click="vm.toggleNcc2022Sort(option, thisTable)"
                                                            />
                                                        </div></th>
                                                        <th>House Energy Rating</th>
                                                        <th>Heating (MJ/m<sup>2</sup>)</th>
                                                        <th>Cooling (MJ/m<sup>2</sup>)</th>
                                                        <th>Total (MJ/m<sup>2</sup>)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr ng-repeat="row in thisTable.ncc2022OffsetList track by $index">
                                                        <td> {{row.northOffset}} </td>
                                                        <td> <input formatted-number id="table_{{thisTable.id}}_row_{{$index}}_0" ng-keydown="vm.keyPressedOnInput(thisTable.id, $event, $index, 0)" ng-model="row.her" ng-model-options="{updateOn: 'blur'}" ng-focus="vm.validateHouseEnergyRating(row)" ng-change="vm.validateHouseEnergyRating(row)" ng-disabled="!vm.permission_tab_multisim_edit" /> </td>
                                                        <td> <input formatted-number id="table_{{thisTable.id}}_row_{{$index}}_1" ng-keydown="vm.keyPressedOnInput(thisTable.id, $event, $index, 1)" ng-model="row.heating" ng-disabled="!vm.permission_tab_multisim_edit" /> </td>
                                                        <td> <input formatted-number id="table_{{thisTable.id}}_row_{{$index}}_2" ng-keydown="vm.keyPressedOnInput(thisTable.id, $event, $index, 2)" ng-model="row.cooling" ng-disabled="!vm.permission_tab_multisim_edit" /> </td>
                                                        <td> <input formatted-number id="table_{{thisTable.id}}_row_{{$index}}_3" ng-keydown="vm.keyPressedOnInput(thisTable.id, $event, $index, 3)" ng-model="row.total"   ng-disabled="!vm.permission_tab_multisim_edit" /> </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </md-card>

                                    </fieldset>

                                    <md-button redi-enable-roles="assessment_page_(tabs/sub-tabs)__multisim__edit"
                                               ng-if="vm.permission_action_addmultisim"
                                               class="md-raised md-primary add-option-button"
                                               ng-click="vm.addNcc2022(option)"
                                               style="height: 30px; vertical-align: central;">
                                        ADD OPTION
                                    </md-button>
                                </md-card>
                            </md-tab-body>
                        </md-tab>
                    </md-tabs>
                </md-tab>

                <!-- Analytics -->
                <md-tab label="Analytics"
                        ng-if="vm.permission_tab_analytics_view"
                        layout="column"
                        ng-click="vm.navigateToAnalytics()">
                    <md-tab-label>
                        <span>
                            Analytics
                        </span>
                    </md-tab-label>
                    <md-tab-body>
                        <!-- ng-form="buildingZonesForm" -->
                        <!-- Use ng-if with showAnalyticsContent to force complete destruction and re-creation -->
                        <div ng-if="vm.showAnalyticsContent">
                            <md-tabs md-selected="vm.selectedOptionIndex">
                                <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                        layout="column"
                                        ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex">
                                    <md-tab-label>
                                        <span>
                                            {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                            <!-- <i class="fa fa-exclamation-triangle label-orange"
                                               aria-hidden="true"
                                               ng-if="option.proposed.zonesFormErrors === true || option.reference.zonesFormErrors === true">
                                            </i> -->
                                        </span>
                                    </md-tab-label>
                                    <md-tab-body>

                                        <!-- Radio Selector to switch between proposed and reference building zones -->
                                        <div flex="100"
                                             style="margin-top: 20px; margin-bottom: 20px;"
                                             layout="row"
                                             layout-align="left center">

                                            <md-radio-group ng-model="vm.buildingToShow"
                                                            layout="row"
                                                            style="margin-left: 30px;">

                                                <md-radio-button ng-value="'proposed'">
                                                    Proposed Building&nbsp;
                                                </md-radio-button>
                                                <md-radio-button ng-value="'reference'"
                                                                 ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                    {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                                </md-radio-button>
                                            </md-radio-group>
                                        </div>

                                        <zone-summary ng-if="option[vm.buildingToShow] != null"
                                                      source="option[vm.buildingToShow]"
                                                      storeys="option[vm.buildingToShow].storeys"
                                                      ncc-climate-zone="vm.assessment.nccClimateZone.description"
                                                      nathers-climate-zone="vm.assessment.natHERSClimateZone.description"
                                                      option-index="option.optionIndex"
                                                      certification="option.certification"
                                                      sector-determination="option.sectorDetermination"
                                                      envelope-summary-save="option.proposed.envelopeSummary"
                                                      zone-summary-save="option.proposed.zoneSummary">
                                        </zone-summary>

                                    </md-tab-body>
                                </md-tab>
                            </md-tabs>
                        </div>
                    </md-tab-body>
                </md-tab>

                <!-- Results -->
                <md-tab label="Results"
                        ng-if="vm.permission_tab_results_view"
                        ng-click="vm.navigateToTab('results')">
                    <md-tab-label>
                        <span>
                            Results&nbsp;
                        </span>
                    </md-tab-label>
                    <md-tab-body style="overflow: hidden;">

                        <md-tabs md-selected="vm.selectedOptionIndex">
                            <md-tab ng-repeat="option in vm.assessment.allComplianceOptions"
                                    layout="column"
                                    ng-click="vm.buildingSubtabChanged(option); vm.selectedOptionIndex = option.optionIndex;">

                                <md-tab-label>
                                    <span>
                                        {{ option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex}}{{option.isSelected && option.isCompliant ? '*' : ''}}&nbsp;
                                    </span>
                                </md-tab-label>

                                <md-tab-body>

                                    <!-- Radio Selector to switch between proposed and reference buildings -->
                                    <div flex="100"
                                         style="margin-top: 20px; margin-bottom: 20px;"
                                         layout="row"
                                         layout-align="left center">
                                        <md-radio-group ng-model="vm.buildingToShow"
                                                        layout="row"
                                                        style="margin-left: 30px;">

                                            <md-radio-button ng-value="'proposed'">
                                                Proposed Building&nbsp;
                                            </md-radio-button>
                                            <md-radio-button ng-value="'reference'"
                                                             ng-disabled="option.complianceMethod.complianceMethodCode != 'CMPerfSolution' && option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS'">
                                                {{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building' : 'Deemed-to-Satisfy Building'}}&nbsp;
                                            </md-radio-button>
                                        </md-radio-group>
                                    </div>

                                    <results-overview assessment="vm.assessment"
                                                      option="option"
                                                      building="option[vm.buildingToShow]"
                                                      building-type="vm.buildingToShow"
                                                      disabled="vm.isLocked || !vm.permission_tab_results_edit">
                                    </results-overview>
                                </md-tab-body>
                            </md-tab>
                        </md-tabs>

                    </md-tab-body>
                </md-tab>

                <!-- Reports -->
                <md-tab ng-if="vm.permission_tab_reports_view"
                        label="Reports"
                        layout="column"
                        ng-click="vm.navigateToTab('reports')">
                    <fieldset ng-if="vm.assessment.assessmentProjectDetail != null"
                              redi-enable-roles="assessment_page_(tabs/sub-tabs)__reports__edit"
                              id="assessment-report-fieldset"
                              ng-disabled="!vm.permission_tab_reports_edit">
                        <md-card layout-margin>
                            <md-card-header>
                                <span class="md-title">Reports</span>
                                <md-button class="md-raised md-primary"
                                           style="margin: -2px 0 0 20px;"
                                           redi-allow-roles="['assessment_actions__editassessment']"
                                           ng-disabled="vm.isBusy || vm.disableAllActions || !vm.permission_tab_reports_edit"
                                           ng-show="vm.assessment.deleted != true && (vm.assessment.statusCode == 'AIssued' || vm.assessment.statusCode == 'AComplete')"
                                           type="button"
                                           ng-click="vm.regenerateReports()">
                                    Regenerate
                                </md-button>
                            </md-card-header>
                            <md-card-content>

                                <report-files report-files="vm.jobFiles"
                                              assessment-id="vm.assessment.assessmentId"
                                              job-id="vm.assessment.jobId"
                                              disabled="vm.disableAllActions || !vm.permission_tab_reports_edit||
                                                        (vm.assessment.statusCode !== 'AIssued' && vm.assessment.statusCode !== 'AComplete')">
                                </report-files>

                            </md-card-content>
                        </md-card>
                    </fieldset>
                </md-tab>

            </md-tabs>
        </div>
    </div>

    <div class="fixed-action-bar">
        <div data-cc-widget-button-bar
             data-is-modal="vm.isModal">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <div style="display: inline-block;">
                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_actions__editassessment']"
                           ng-disabled="vm.isBusy || vm.disableAllActions || vm.saveDisabled() || (!vm.clientOptionsConfigured) || (!vm.cachedHasSiteMapImageData || !vm.cachedHasAerialMapImageData)"
                           ng-show="vm.assessment.deleted != true"
                           type="button"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-tooltip ng-if="vm.cachedMapImagesNotCaptured"
                            md-direction="top">
                    {{vm.cachedMapImagesNotCaptured}}
                </md-tooltip>
            </div>
            <md-button class="md-raised"
                       style="padding-left: 20px; padding-right: 20px;"
                       redi-allow-roles="['assessment_actions__deleteassessment']"
                       ng-disabled="vm.disableAllActions"
                       ng-show="vm.newRecord==false && vm.assessment.deleted!=true && !vm.isLocked && vm.assessmentList.length > 1"
                       ng-confirm-click="vm.delete(false)"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you wish to delete assessment version {{vm.assessment.assessmentProjectDetail.assessmentVersion}}.">
                Delete Version {{vm.assessment.assessmentProjectDetail.assessmentVersion}}
            </md-button>
            <md-button class="md-raised md-warn"
                       style="padding-left: 20px; padding-right: 20px;"
                       redi-allow-roles="['assessment_actions__deleteassessment']"
                       ng-disabled="vm.disableAllActions"
                       ng-show="vm.newRecord==false && vm.assessment.deleted!=true && (!vm.isLocked || (vm.isLocked && vm.assessment.job.statusCode == 'JCancelled'))"
                       ng-confirm-click="vm.delete(true)"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you wish to delete the entire assessment and all versions.">
                Delete Assessment
            </md-button>
            <md-button class="md-raised"
                       redi-allow-roles="['assessment_actions__deleteassessment']"
                       ng-show="vm.assessment.deleted == true"
                       ng-disabled="vm.disableAllActions"
                       ng-confirm-click="vm.undoDelete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to RESTORE this record.">
                Restore
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()"
                       ng-disabled="vm.disableAllActions">
                Cancel
            </md-button>
            <div class="clearfix"></div>
        </div>
    </div>

</form>

<!-- This is a div only shown for milliseconds when we need to auto-render a map image on confirmed address change! #Aerial Image -->
<div id="remote_map"
     style="width: 100%;
            height: 1200px;
            display: none;
            visibility: hidden;
            position: absolute;
            left: 0;
            top: 0;">
</div>

<!-- Remote map, should never be seen -->
<div id="remote_site_map"
        style="width: 100%;
            height: 1100px;
            display: none;
            visibility: hidden;
            position: absolute;
            left: 0;
            top: 0;">
</div>

<style>
    .assessment-content fieldset {
        padding: 0;
    }
    .assessment-content md-card {
        margin: 0;
        margin-top: 12px;
    }

    .ncc2022NoDataSaved {
        margin: 27px 20px 10px 20px;
        font-size: 14px;
    }

    .ncc2022OuterCard {
        width: 100%;
        min-height: 74px;
    }

    .ncc2022CardsContainer {
        display: flex;
        justify-content: space-between;
    }

    .ncc2022Card {
        margin: 10px 20px !important;
        width: 47%;
        padding: 30px;
    }

    .ncc2022FieldContainer {
        width: 100%;
        margin: auto;
        position: relative;
    }

        .ncc2022Table {
            border-collapse: collapse;
            width: 100%;
            margin: 0 0 30px 0;
        }

            .northOffsetHeader {
                display: flex;
                justify-content: center;
                align-items: center;
                column-gap: 10px;
            }

                .northOffsetSortButton {
                    padding: 2px 2px;
                    border-radius: 5px;
                    cursor: pointer;
                    user-select: none;
                }
                .sortByAlphaSelected {
                    background-color: #b2ff59;
                }
                .northOffsetSortButtonFlipped {
                    cursor: default;
                }

            .ncc2022Field {
                width: 400px;
                margin-bottom: -5px;
            }
            .ncc2022-active-remove-container {
                position: absolute;
                right: -10px;
                top: 15px;
            }
                .ncc2022-active-switch {
                    margin-top: 4px;
                }
                .ncc2022-remove-button {
                    margin-top: 12px;
                    margin-left: 0 !important;
                }
            .ncc2022Table td, .ncc2022Table th, .ncc2022Table input {
                border: solid 1px black;
                padding: 1px 1px;
                width: 60px;
                height: 30px;
                text-align: center;
            }
            .ncc2022Table input {
                width: 100%;
                height: 100%;
                padding: 3px;
                box-sizing: border-box;
                border: none;
                border-radius: 0;
            }
    .add-option-button {
        width: min-content;
        margin: 15px;
    }

    .lotDescriptionHeightOverride > md-select-menu,
    .lotDescriptionHeightOverride > md-select-menu > md-content {
        max-height: max-content;
    }
</style>